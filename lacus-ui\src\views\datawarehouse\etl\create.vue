<template>
  <el-dialog
    :title="isEdit ? '编辑ETL任务' : '新增ETL任务'"
    v-model="dialogVisible"
    width="1200px"
    append-to-body
    @close="handleCancel"
  >

      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <el-card class="mb-4">
          <template #header>基本信息</template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="任务名称" prop="taskName">
                <el-input v-model="form.taskName" placeholder="请输入任务名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务描述" prop="description">
                <el-input v-model="form.description" placeholder="请输入任务描述"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="源层级" prop="sourceLayer">
                <el-select v-model="form.sourceLayer" placeholder="请选择源层级" @change="handleSourceLayerChange">
                  <el-option label="ODS" value="ODS"/>
                  <el-option label="DWD" value="DWD"/>
                  <el-option label="DWS" value="DWS"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="目标层级" prop="targetLayer">
                <el-select v-model="form.targetLayer" placeholder="请选择目标层级" @change="handleTargetLayerChange">
                  <el-option v-for="layer in targetLayerOptions" :key="layer" :label="layer" :value="layer"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="调度方式" prop="scheduleType">
                <el-select v-model="form.scheduleType" placeholder="请选择调度方式">
                  <el-option label="手动执行" value="MANUAL"/>
                  <el-option label="定时调度" value="CRON"/>
                  <el-option label="实时处理" value="REALTIME"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="form.scheduleType === 'CRON'">
            <el-col :span="12">
              <el-form-item label="Cron表达式" prop="cronExpression">
                <el-input v-model="form.cronExpression" placeholder="请输入Cron表达式，如：0 0 2 * * ?"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时区" prop="timezone">
                <el-select v-model="form.timezone" placeholder="请选择时区">
                  <el-option label="Asia/Shanghai" value="Asia/Shanghai"/>
                  <el-option label="UTC" value="UTC"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 数据源配置 -->
        <el-card class="mb-4">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>数据源配置</span>
              <el-button type="primary" size="small" @click="handleAddSourceTable">添加源表</el-button>
            </div>
          </template>
          <el-table :data="form.sourceTables" style="width: 100%">
            <el-table-column label="序号" type="index" width="60"/>
            <el-table-column label="源表名" prop="tableName" width="200">
              <template #default="scope">
                <el-select v-model="scope.row.tableName" placeholder="请选择源表" filterable>
                  <el-option
                    v-for="table in sourceTableOptions"
                    :key="table.tableName"
                    :label="`${table.tableName} (${table.comment || '无描述'})`"
                    :value="table.tableName"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="表别名" prop="alias" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.alias" placeholder="请输入表别名"/>
              </template>
            </el-table-column>
            <el-table-column label="过滤条件" prop="whereCondition">
              <template #default="scope">
                <el-input v-model="scope.row.whereCondition" placeholder="请输入WHERE条件，如：status = 1"/>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleRemoveSourceTable(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 目标表配置 -->
        <el-card class="mb-4">
          <template #header>目标表配置</template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="目标表名" prop="targetTable">
                <el-select v-model="form.targetTable" placeholder="请选择或输入目标表名" filterable allow-create>
                  <el-option
                    v-for="table in targetTableOptions"
                    :key="table.tableName"
                    :label="`${table.tableName} (${table.comment || '无描述'})`"
                    :value="table.tableName"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="写入模式" prop="writeMode">
                <el-select v-model="form.writeMode" placeholder="请选择写入模式">
                  <el-option label="覆盖写入 (DROP & CREATE)" value="OVERWRITE"/>
                  <el-option label="追加写入 (INSERT)" value="APPEND"/>
                  <el-option label="更新插入 (UPSERT)" value="UPSERT"/>
                  <el-option label="自动建表 (AUTO_CREATE)" value="AUTO_CREATE"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="form.writeMode === 'UPSERT'">
            <el-col :span="24">
              <el-form-item label="主键字段" prop="primaryKeys">
                <el-select v-model="form.primaryKeys" multiple placeholder="请选择主键字段">
                  <el-option v-for="field in targetFieldOptions" :key="field" :label="field" :value="field"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 字段映射配置 -->
        <el-card class="mb-4">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>字段映射配置</span>
              <div>
                <el-button type="success" size="small" @click="handleAutoMapping">智能映射</el-button>
                <el-button type="info" size="small" @click="handlePreview">预览结果</el-button>
                <el-button type="primary" size="small" @click="handleAddFieldMapping">添加字段</el-button>
              </div>
            </div>
          </template>
          <el-table :data="form.fieldMappings" style="width: 100%">
            <el-table-column label="序号" type="index" width="60"/>
            <el-table-column label="目标字段" prop="targetField" width="200">
              <template #default="scope">
                <el-input v-model="scope.row.targetField" placeholder="请输入目标字段名"/>
              </template>
            </el-table-column>
            <el-table-column label="字段类型" prop="fieldType" width="120">
              <template #default="scope">
                <el-select v-model="scope.row.fieldType" placeholder="类型">
                  <el-option label="STRING" value="STRING"/>
                  <el-option label="INT" value="INT"/>
                  <el-option label="BIGINT" value="BIGINT"/>
                  <el-option label="DECIMAL" value="DECIMAL"/>
                  <el-option label="DATE" value="DATE"/>
                  <el-option label="DATETIME" value="DATETIME"/>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="转换表达式" prop="expression">
              <template #default="scope">
                <el-input v-model="scope.row.expression" placeholder="如：CONCAT(first_name, ' ', last_name)"/>
              </template>
            </el-table-column>
            <el-table-column label="默认值" prop="defaultValue" width="120">
              <template #default="scope">
                <el-input v-model="scope.row.defaultValue" placeholder="默认值"/>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleRemoveFieldMapping(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 数据质量配置 -->
        <el-card class="mb-4">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>数据质量配置</span>
              <el-button type="primary" size="small" @click="handleAddQualityRule">添加规则</el-button>
            </div>
          </template>
          <el-table :data="form.qualityRules" style="width: 100%">
            <el-table-column label="序号" type="index" width="60"/>
            <el-table-column label="规则名称" prop="ruleName" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.ruleName" placeholder="请输入规则名称"/>
              </template>
            </el-table-column>
            <el-table-column label="检查字段" prop="checkField" width="150">
              <template #default="scope">
                <el-select v-model="scope.row.checkField" placeholder="选择字段">
                  <el-option v-for="field in form.fieldMappings" :key="field.targetField"
                    :label="field.targetField" :value="field.targetField"/>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="规则类型" prop="ruleType" width="120">
              <template #default="scope">
                <el-select v-model="scope.row.ruleType" placeholder="规则类型">
                  <el-option label="非空检查" value="NOT_NULL"/>
                  <el-option label="唯一性检查" value="UNIQUE"/>
                  <el-option label="范围检查" value="RANGE"/>
                  <el-option label="格式检查" value="FORMAT"/>
                  <el-option label="自定义SQL" value="CUSTOM_SQL"/>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="规则配置" prop="ruleConfig">
              <template #default="scope">
                <el-input v-model="scope.row.ruleConfig" placeholder="如：min=0,max=100 或正则表达式"/>
              </template>
            </el-table-column>
            <el-table-column label="严重级别" prop="severity" width="100">
              <template #default="scope">
                <el-select v-model="scope.row.severity" placeholder="级别">
                  <el-option label="警告" value="WARNING"/>
                  <el-option label="错误" value="ERROR"/>
                  <el-option label="致命" value="FATAL"/>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleRemoveQualityRule(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
          <el-button type="success" @click="handleSaveAndRun" :loading="saving">保存并执行</el-button>
        </div>
      </template>
    </el-dialog>
</template>

<script setup name="EtlTaskCreate">
import { getEtlTask, addEtlTask, updateEtlTask, runEtlTask, previewEtlResult, getFieldMappingSuggestions } from '@/api/datawarehouse/etl';
import { getTablesByLayer } from '@/api/metadata/table';

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: [String, Number],
    default: null
  }
});

// 组件事件
const emit = defineEmits(['update:visible', 'success']);

const { proxy } = getCurrentInstance();

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const isEdit = computed(() => !!props.taskId);
const saving = ref(false);
const sourceTableOptions = ref([]);
const targetTableOptions = ref([]);
const targetFieldOptions = ref([]);

const targetLayerOptions = computed(() => {
  const layerMap = {
    'ODS': ['DWD'],
    'DWD': ['DWS'],
    'DWS': ['ADS']
  };
  return layerMap[form.value.sourceLayer] || [];
});


const form = ref({
  taskName: '',
  description: '',
  sourceLayer: 'ODS',
  targetLayer: 'DWD',
  scheduleType: 'MANUAL',
  cronExpression: '0 0 2 * * ?',
  timezone: 'Asia/Shanghai',
  targetTable: '',
  writeMode: 'OVERWRITE',
  primaryKeys: [],
  sourceTables: [],
  fieldMappings: [],
  qualityRules: []
});

const rules = {
  taskName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  sourceLayer: [{ required: true, message: '请选择源层级', trigger: 'change' }],
  targetLayer: [{ required: true, message: '请选择目标层级', trigger: 'change' }],
  scheduleType: [{ required: true, message: '请选择调度方式', trigger: 'change' }],
  targetTable: [{ required: true, message: '请输入目标表名', trigger: 'blur' }],
  writeMode: [{ required: true, message: '请选择写入模式', trigger: 'change' }]
};


/** 源层级变化处理 */
function handleSourceLayerChange() {
  // 自动设置目标层级
  form.value.targetLayer = targetLayerOptions.value[0] || '';
  // 加载源表列表
  loadSourceTables();
  // 同时加载目标表列表
  loadTargetTables();
}

/** 加载源表列表 */
function loadSourceTables() {
  if (form.value.sourceLayer) {
    getTablesByLayer(form.value.sourceLayer).then(response => {
      sourceTableOptions.value = response || [];
    }).catch(error => {
      console.error('加载源表列表失败:', error);
      proxy.$modal.msgError('加载源表列表失败');
    });
  }
}

/** 添加源表 */
function handleAddSourceTable() {
  form.value.sourceTables.push({
    tableName: '',
    alias: '',
    whereCondition: ''
  });
}

/** 删除源表 */
function handleRemoveSourceTable(index) {
  form.value.sourceTables.splice(index, 1);
}

/** 添加字段映射 */
function handleAddFieldMapping() {
  form.value.fieldMappings.push({
    targetField: '',
    fieldType: 'STRING',
    expression: '',
    defaultValue: ''
  });
}

/** 删除字段映射 */
function handleRemoveFieldMapping(index) {
  form.value.fieldMappings.splice(index, 1);
}

/** 智能映射 */
function handleAutoMapping() {
  if (form.value.sourceTables.length === 0) {
    proxy.$modal.msgWarning('请先添加源表');
    return;
  }

  const sourceTables = form.value.sourceTables.map(t => t.tableName).filter(Boolean);
  if (sourceTables.length === 0) {
    proxy.$modal.msgWarning('请先选择源表');
    return;
  }

  getFieldMappingSuggestions(form.value.sourceLayer, form.value.targetLayer, sourceTables)
    .then(response => {
      form.value.fieldMappings = response.data || [];
      proxy.$modal.msgSuccess('智能映射完成');
    })
    .catch(() => {
      proxy.$modal.msgError('智能映射失败');
    });
}

/** 预览结果 */
function handlePreview() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      previewEtlResult(form.value).then(() => {
        proxy.$modal.msgSuccess('预览功能开发中...');
        // 这里可以显示预览结果对话框
      });
    }
  });
}

/** 目标层级变化处理 */
function handleTargetLayerChange() {
  loadTargetTables();
}

/** 加载目标表列表 */
function loadTargetTables() {
  if (form.value.targetLayer) {
    getTablesByLayer(form.value.targetLayer).then(response => {
      targetTableOptions.value = response || [];
    }).catch(error => {
      console.error('加载目标表列表失败:', error);
      proxy.$modal.msgError('加载目标表列表失败');
    });
  }
}

/** 添加数据质量规则 */
function handleAddQualityRule() {
  form.value.qualityRules.push({
    ruleName: '',
    checkField: '',
    ruleType: 'NOT_NULL',
    ruleConfig: '',
    severity: 'WARNING'
  });
}

/** 删除数据质量规则 */
function handleRemoveQualityRule(index) {
  form.value.qualityRules.splice(index, 1);
}

/** 保存 */
function handleSave() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      saving.value = true;
      const api = isEdit.value ? updateEtlTask : addEtlTask;
      const data = isEdit.value ? { ...form.value, id: props.taskId } : form.value;

      api(data).then(() => {
        proxy.$modal.msgSuccess(isEdit.value ? '修改成功' : '新增成功');
        emit('success');
      }).finally(() => {
        saving.value = false;
      });
    }
  });
}

/** 保存并执行 */
function handleSaveAndRun() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      saving.value = true;
      const api = isEdit.value ? updateEtlTask : addEtlTask;
      const data = isEdit.value ? { ...form.value, id: props.taskId } : form.value;

      api(data).then(response => {
        const taskId = response.data || props.taskId;
        return runEtlTask(taskId);
      }).then(() => {
        proxy.$modal.msgSuccess('保存并执行成功');
        emit('success');
      }).finally(() => {
        saving.value = false;
      });
    }
  });
}

/** 取消 */
function handleCancel() {
  dialogVisible.value = false;
}

/** 监听弹窗显示状态 */
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
    if (isEdit.value && props.taskId) {
      getEtlTask(props.taskId).then(response => {
        form.value = response.data;
        if (form.value.sourceLayer) {
          loadSourceTables();
        }
        if (form.value.targetLayer) {
          loadTargetTables();
        }
      });
    } else {
      loadSourceTables();
      loadTargetTables();
    }
  }
});

/** 重置表单 */
function resetForm() {
  form.value = {
    taskName: '',
    description: '',
    sourceLayer: 'ODS',
    targetLayer: 'DWD',
    scheduleType: 'MANUAL',
    sourceTables: [],
    targetTable: '',
    writeMode: 'OVERWRITE',
    fieldMappings: [],
    qualityRules: []
  };
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.mb-4 {
  margin-bottom: 16px;
}

.el-card {
  margin-bottom: 16px;
}

.el-card:last-child {
  margin-bottom: 0;
}
</style>
