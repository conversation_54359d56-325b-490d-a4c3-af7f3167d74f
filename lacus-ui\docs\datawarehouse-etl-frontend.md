# 数据仓库ETL前端系统

## 系统概述

本系统是一个基于Vue3 + Element Plus的数据仓库ETL配置和管理前端系统，支持ODS→DWD→DWS→ADS四层数据仓库架构的ETL任务动态配置、调度管理、数据质量监控和血缘关系可视化。

## 核心功能模块

### 1. ETL任务管理 (`/datawarehouse/etl`)

#### 功能特性
- **多层级转换支持**: 支持ODS→DWD、DWD→DWS、DWS→ADS的数据层级转换
- **动态配置**: 前端可视化配置ETL任务，无需编写SQL
- **字段映射**: 支持复杂的字段转换表达式和数据类型映射
- **数据质量规则**: 内置数据质量检查规则配置
- **智能映射**: 基于源表结构自动推荐字段映射关系

#### 主要页面
- **任务列表页** (`index.vue`): 展示所有ETL任务，支持搜索、筛选、批量操作
- **任务创建/编辑页** (`create.vue`): 可视化配置ETL任务的所有参数
- **任务详情页** (`detail.vue`): 查看任务配置详情和执行历史

#### 配置项说明
```javascript
// ETL任务配置结构
{
  taskName: '任务名称',
  description: '任务描述',
  sourceLayer: 'ODS|DWD|DWS',        // 源数据层级
  targetLayer: 'DWD|DWS|ADS',        // 目标数据层级
  scheduleType: 'MANUAL|CRON|REALTIME', // 调度方式
  cronExpression: '0 0 2 * * ?',     // Cron表达式
  sourceTables: [                    // 源表配置
    {
      tableName: '源表名',
      alias: '表别名',
      whereCondition: 'WHERE条件'
    }
  ],
  fieldMappings: [                   // 字段映射配置
    {
      targetField: '目标字段名',
      fieldType: '字段类型',
      expression: '转换表达式',
      defaultValue: '默认值'
    }
  ],
  qualityRules: [                    // 数据质量规则
    {
      ruleName: '规则名称',
      checkField: '检查字段',
      ruleType: 'NOT_NULL|UNIQUE|RANGE|FORMAT|CUSTOM_SQL',
      ruleConfig: '规则配置',
      severity: 'WARNING|ERROR|FATAL'
    }
  ]
}
```

### 2. 数据血缘关系 (`/datawarehouse/lineage`)

#### 功能特性
- **可视化血缘图**: 基于ECharts的交互式血缘关系图
- **多层级展示**: 清晰展示ODS、DWD、DWS、ADS各层级的数据流向
- **节点详情**: 点击节点查看表结构、字段信息、统计数据
- **血缘追踪**: 支持正向和反向血缘关系追踪

#### 图例说明
- 🟢 ODS层表 (绿色)
- 🔵 DWD层表 (蓝色)  
- 🟡 DWS层表 (黄色)
- 🔴 ADS层表 (红色)
- 🟣 ETL任务 (紫色)

### 3. 数据质量监控 (`/datawarehouse/quality`)

#### 功能特性
- **质量概览**: 实时展示数据质量统计指标
- **质量趋势**: 可视化展示质量变化趋势图表
- **质量评分**: 基于规则的自动化质量评分系统
- **异常告警**: 质量异常自动告警和处理流程
- **质量报告**: 生成详细的数据质量分析报告

#### 质量规则类型
- **非空检查** (`NOT_NULL`): 检查字段是否为空
- **唯一性检查** (`UNIQUE`): 检查字段值是否唯一
- **范围检查** (`RANGE`): 检查数值是否在指定范围内
- **格式检查** (`FORMAT`): 基于正则表达式的格式验证
- **自定义SQL** (`CUSTOM_SQL`): 自定义SQL规则检查

### 4. 调度管理 (`/datawarehouse/schedule`)

#### 功能特性
- **调度概览**: 展示调度任务的整体运行状态
- **调度日历**: 日历视图展示调度计划和执行情况
- **Cron配置**: 支持复杂的Cron表达式配置
- **依赖管理**: 支持任务间的依赖关系配置
- **执行监控**: 实时监控任务执行状态和性能指标

#### 调度状态
- **启用** (`ENABLED`): 调度任务正常运行
- **禁用** (`DISABLED`): 调度任务已停用
- **暂停** (`PAUSED`): 调度任务临时暂停

## API接口设计

### ETL任务管理API (`/api/datawarehouse/etl.js`)
```javascript
// 主要接口
listEtlTask(query)              // 查询ETL任务列表
getEtlTask(taskId)              // 获取ETL任务详情
addEtlTask(data)                // 新增ETL任务
updateEtlTask(data)             // 更新ETL任务
runEtlTask(taskId)              // 执行ETL任务
getExecutionHistory(taskId)     // 获取执行历史
previewEtlResult(data)          // 预览ETL结果
getFieldMappingSuggestions()    // 获取字段映射建议
```

### 数据质量API (`/api/datawarehouse/quality.js`)
```javascript
// 主要接口
listQualityMonitor(query)       // 查询质量监控列表
runQualityCheck(id)             // 执行质量检查
getQualityStats()               // 获取质量统计
getQualityTrend(period)         // 获取质量趋势
getQualityReport(id)            // 获取质量报告
```

### 调度管理API (`/api/datawarehouse/schedule.js`)
```javascript
// 主要接口
listScheduleJob(query)          // 查询调度任务列表
triggerScheduleJob(id)          // 触发调度任务
pauseScheduleJob(id)            // 暂停调度任务
resumeScheduleJob(id)           // 恢复调度任务
getScheduleStats()              // 获取调度统计
getTodaySchedules()             // 获取今日调度
```

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **图表库**: ECharts (用于血缘关系图和趋势图)
- **HTTP客户端**: Axios
- **路由管理**: Vue Router
- **状态管理**: Pinia (如需要)

## 部署说明

### 1. 路由配置
将 `datawarehouse.js` 路由模块添加到主路由配置中：

```javascript
// router/index.js
import datawarehouseRouter from './modules/datawarehouse'

const routes = [
  // ... 其他路由
  datawarehouseRouter
]
```

### 2. 菜单配置
在后端菜单管理中添加数据仓库相关菜单项，确保权限控制正确配置。

### 3. 后端接口
确保后端提供对应的REST API接口，接口路径需要与前端API调用保持一致。

## 扩展功能

### 1. 实时监控
- 任务执行状态实时更新
- 性能指标实时监控
- 异常告警实时推送

### 2. 配置模板
- ETL任务配置模板
- 质量规则模板
- 调度配置模板

### 3. 数据预览
- 源数据预览
- 转换结果预览
- 质量检查结果预览

### 4. 导入导出
- ETL配置导入导出
- 质量规则导入导出
- 调度配置导入导出

## 使用建议

1. **任务命名**: 建议使用有意义的任务名称，包含源层级和目标层级信息
2. **字段映射**: 充分利用智能映射功能，减少手工配置工作量
3. **质量规则**: 根据业务需求配置合适的数据质量规则
4. **调度策略**: 合理安排调度时间，避免资源冲突
5. **监控告警**: 及时关注任务执行状态和质量监控结果

## 注意事项

1. 确保后端API接口的稳定性和性能
2. 大数据量场景下注意前端分页和性能优化
3. 复杂ETL任务建议先进行小数据量测试
4. 定期备份重要的ETL配置和质量规则
5. 建立完善的权限控制和操作审计机制
