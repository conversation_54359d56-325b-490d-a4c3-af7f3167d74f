import request from '@/utils/request'

// 查询调度任务列表
export function listScheduleJob(query) {
  return request({
    url: '/datawarehouse/schedule/list',
    method: 'get',
    params: query
  })
}

// 查询调度任务详细
export function getScheduleJob(id) {
  return request({
    url: '/datawarehouse/schedule/' + id,
    method: 'get'
  })
}

// 新增调度任务
export function addScheduleJob(data) {
  return request({
    url: '/datawarehouse/schedule',
    method: 'post',
    data: data
  })
}

// 修改调度任务
export function updateScheduleJob(data) {
  return request({
    url: '/datawarehouse/schedule',
    method: 'put',
    data: data
  })
}

// 删除调度任务
export function deleteScheduleJob(id) {
  return request({
    url: '/datawarehouse/schedule/' + id,
    method: 'delete'
  })
}

// 触发调度任务
export function triggerScheduleJob(id) {
  return request({
    url: '/datawarehouse/schedule/trigger/' + id,
    method: 'post'
  })
}

// 暂停调度任务
export function pauseScheduleJob(id) {
  return request({
    url: '/datawarehouse/schedule/pause/' + id,
    method: 'post'
  })
}

// 恢复调度任务
export function resumeScheduleJob(id) {
  return request({
    url: '/datawarehouse/schedule/resume/' + id,
    method: 'post'
  })
}

// 获取调度统计信息
export function getScheduleStats() {
  return request({
    url: '/datawarehouse/schedule/stats',
    method: 'get'
  })
}

// 获取今日调度任务
export function getTodaySchedules() {
  return request({
    url: '/datawarehouse/schedule/today',
    method: 'get'
  })
}

// 获取日历调度数据
export function getCalendarSchedules(year, month) {
  return request({
    url: '/datawarehouse/schedule/calendar',
    method: 'get',
    params: { year, month }
  })
}

// 获取调度执行历史
export function getScheduleHistory(id, query) {
  return request({
    url: '/datawarehouse/schedule/history/' + id,
    method: 'get',
    params: query
  })
}

// 获取调度执行日志
export function getScheduleLog(executionId) {
  return request({
    url: '/datawarehouse/schedule/log/' + executionId,
    method: 'get'
  })
}

// 验证Cron表达式
export function validateCronExpression(expression) {
  return request({
    url: '/datawarehouse/schedule/validate-cron',
    method: 'post',
    data: { expression }
  })
}

// 获取Cron表达式下次执行时间
export function getNextFireTimes(expression, count = 5) {
  return request({
    url: '/datawarehouse/schedule/next-fire-times',
    method: 'post',
    data: { expression, count }
  })
}

// 获取调度依赖关系
export function getScheduleDependencies(id) {
  return request({
    url: '/datawarehouse/schedule/dependencies/' + id,
    method: 'get'
  })
}

// 设置调度依赖
export function setScheduleDependencies(id, dependencies) {
  return request({
    url: '/datawarehouse/schedule/dependencies/' + id,
    method: 'post',
    data: { dependencies }
  })
}

// 获取调度任务监控数据
export function getScheduleMonitor(id) {
  return request({
    url: '/datawarehouse/schedule/monitor/' + id,
    method: 'get'
  })
}

// 获取调度性能指标
export function getScheduleMetrics(id, timeRange) {
  return request({
    url: '/datawarehouse/schedule/metrics/' + id,
    method: 'get',
    params: { timeRange }
  })
}

// 导出调度配置
export function exportScheduleConfig(id) {
  return request({
    url: '/datawarehouse/schedule/export/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 导入调度配置
export function importScheduleConfig(data) {
  return request({
    url: '/datawarehouse/schedule/import',
    method: 'post',
    data: data
  })
}

// 批量操作调度任务
export function batchOperateSchedule(operation, ids) {
  return request({
    url: '/datawarehouse/schedule/batch-operate',
    method: 'post',
    data: { operation, ids }
  })
}

// 获取调度告警规则
export function getScheduleAlertRules(id) {
  return request({
    url: '/datawarehouse/schedule/alert-rules/' + id,
    method: 'get'
  })
}

// 设置调度告警规则
export function setScheduleAlertRules(id, rules) {
  return request({
    url: '/datawarehouse/schedule/alert-rules/' + id,
    method: 'post',
    data: { rules }
  })
}

// 获取调度任务模板
export function getScheduleTemplates() {
  return request({
    url: '/datawarehouse/schedule/templates',
    method: 'get'
  })
}

// 从模板创建调度任务
export function createFromTemplate(templateId, data) {
  return request({
    url: '/datawarehouse/schedule/create-from-template/' + templateId,
    method: 'post',
    data: data
  })
}
