package com.lacus.common.enums;

/**
 * 执行状态枚举
 * 
 * <AUTHOR>
 */
public enum ExecutionStatus {
    
    /**
     * 等待执行
     */
    PENDING("PENDING", "等待执行"),
    
    /**
     * 正在执行
     */
    RUNNING("RUNNING", "正在执行"),
    
    /**
     * 执行成功
     */
    SUCCESS("SUCCESS", "执行成功"),
    
    /**
     * 执行失败
     */
    FAILED("FAILED", "执行失败"),
    
    /**
     * 已停止
     */
    STOPPED("STOPPED", "已停止"),
    
    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消");
    
    private final String code;
    private final String description;
    
    ExecutionStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
}

/**
 * 写入模式枚举
 * 
 * <AUTHOR>
 */
public enum WriteMode {
    
    /**
     * 自动建表
     */
    AUTO_CREATE("AUTO_CREATE", "自动建表"),
    
    /**
     * 覆盖写入
     */
    OVERWRITE("OVERWRITE", "覆盖写入"),
    
    /**
     * 追加写入
     */
    APPEND("APPEND", "追加写入"),
    
    /**
     * 更新插入
     */
    UPSERT("UPSERT", "更新插入");
    
    private final String code;
    private final String description;
    
    WriteMode(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
}

/**
 * 数据层级枚举
 * 
 * <AUTHOR>
 */
public enum DataLayer {
    
    /**
     * 操作数据存储层
     */
    ODS("ODS", "操作数据存储层", "ods_db"),
    
    /**
     * 数据仓库明细层
     */
    DWD("DWD", "数据仓库明细层", "dwd_db"),
    
    /**
     * 数据仓库汇总层
     */
    DWS("DWS", "数据仓库汇总层", "dws_db"),
    
    /**
     * 应用数据服务层
     */
    ADS("ADS", "应用数据服务层", "ads_db");
    
    private final String code;
    private final String description;
    private final String databaseName;
    
    DataLayer(String code, String description, String databaseName) {
        this.code = code;
        this.description = description;
        this.databaseName = databaseName;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getDatabaseName() {
        return databaseName;
    }
    
    /**
     * 根据代码获取数据层级
     */
    public static DataLayer fromCode(String code) {
        for (DataLayer layer : values()) {
            if (layer.getCode().equals(code)) {
                return layer;
            }
        }
        throw new IllegalArgumentException("未知的数据层级代码: " + code);
    }
}
