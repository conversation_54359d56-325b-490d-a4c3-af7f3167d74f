<template>
  <el-dialog 
    :title="isEdit ? '编辑调度任务' : '新增调度任务'" 
    v-model="dialogVisible" 
    width="800px" 
    append-to-body
    @close="handleCancel"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联ETL任务" prop="taskId">
            <el-select v-model="form.taskId" placeholder="请选择ETL任务" style="width: 100%" @change="handleTaskChange">
              <el-option 
                v-for="task in etlTaskList" 
                :key="task.id" 
                :label="task.taskName" 
                :value="task.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="form.taskName" placeholder="请输入任务名称" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="Cron表达式" prop="cronExpression">
            <el-input v-model="form.cronExpression" placeholder="请输入Cron表达式">
              <template #append>
                <el-button @click="handleValidateCron">验证</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时区" prop="timezone">
            <el-select v-model="form.timezone" placeholder="请选择时区" style="width: 100%">
              <el-option label="Asia/Shanghai" value="Asia/Shanghai"/>
              <el-option label="UTC" value="UTC"/>
              <el-option label="America/New_York" value="America/New_York"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="调度状态" prop="scheduleStatus">
            <el-radio-group v-model="form.scheduleStatus">
              <el-radio label="ENABLED">启用</el-radio>
              <el-radio label="DISABLED">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大重试次数" prop="maxRetryCount">
            <el-input-number v-model="form.maxRetryCount" :min="0" :max="10" style="width: 100%"/>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="超时时间(秒)" prop="timeoutSeconds">
            <el-input-number v-model="form.timeoutSeconds" :min="60" :max="86400" style="width: 100%"/>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="依赖配置" prop="dependenciesConfig">
        <el-input
          v-model="form.dependenciesConfig"
          type="textarea"
          :rows="3"
          placeholder="请输入依赖配置（JSON格式）"
        />
      </el-form-item>
      
      <el-form-item label="告警配置" prop="alertConfig">
        <el-input
          v-model="form.alertConfig"
          type="textarea"
          :rows="3"
          placeholder="请输入告警配置（JSON格式）"
        />
      </el-form-item>
      
      <el-form-item label="调度参数" prop="scheduleParams">
        <el-input
          v-model="form.scheduleParams"
          type="textarea"
          :rows="3"
          placeholder="请输入调度参数（JSON格式）"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="ScheduleCreate">
import { getScheduleJob, addScheduleJob, updateScheduleJob, validateCronExpression } from '@/api/datawarehouse/schedule';
import { listEtlTask } from '@/api/datawarehouse/etl';

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  scheduleId: {
    type: [String, Number],
    default: null
  }
});

// 组件事件
const emit = defineEmits(['update:visible', 'success']);

const { proxy } = getCurrentInstance();

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const isEdit = computed(() => !!props.scheduleId);
const saving = ref(false);
const etlTaskList = ref([]);

const data = reactive({
  form: {
    taskId: null,
    taskName: '',
    cronExpression: '',
    timezone: 'Asia/Shanghai',
    scheduleStatus: 'ENABLED',
    maxRetryCount: 3,
    timeoutSeconds: 3600,
    dependenciesConfig: '',
    alertConfig: '',
    scheduleParams: ''
  },
  rules: {
    taskId: [
      { required: true, message: "关联ETL任务不能为空", trigger: "change" }
    ],
    taskName: [
      { required: true, message: "任务名称不能为空", trigger: "blur" }
    ],
    cronExpression: [
      { required: true, message: "Cron表达式不能为空", trigger: "blur" }
    ],
    timezone: [
      { required: true, message: "时区不能为空", trigger: "change" }
    ],
    scheduleStatus: [
      { required: true, message: "调度状态不能为空", trigger: "change" }
    ]
  }
});

const { form, rules } = toRefs(data);

/** 获取ETL任务列表 */
function getEtlTaskList() {
  listEtlTask({ pageNum: 1, pageSize: 1000, status: 1 }).then(response => {
    etlTaskList.value = response.rows || [];
  });
}

/** ETL任务变化 */
function handleTaskChange(taskId) {
  const selectedTask = etlTaskList.value.find(task => task.id === taskId);
  if (selectedTask) {
    form.value.taskName = selectedTask.taskName + "_调度";
  }
}

/** 验证Cron表达式 */
function handleValidateCron() {
  if (!form.value.cronExpression) {
    proxy.$modal.msgWarning("请先输入Cron表达式");
    return;
  }
  
  validateCronExpression(form.value.cronExpression).then(response => {
    if (response.data) {
      proxy.$modal.msgSuccess("Cron表达式格式正确");
    } else {
      proxy.$modal.msgError("Cron表达式格式错误");
    }
  });
}

/** 保存 */
function handleSave() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      saving.value = true;
      const api = isEdit.value ? updateScheduleJob : addScheduleJob;
      const data = isEdit.value ? { ...form.value, id: props.scheduleId } : form.value;

      api(data).then(() => {
        proxy.$modal.msgSuccess(isEdit.value ? '修改成功' : '新增成功');
        emit('success');
      }).finally(() => {
        saving.value = false;
      });
    }
  });
}

/** 取消 */
function handleCancel() {
  dialogVisible.value = false;
}

/** 监听弹窗显示状态 */
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
    getEtlTaskList();
    if (isEdit.value && props.scheduleId) {
      getScheduleJob(props.scheduleId).then(response => {
        form.value = response.data;
      });
    }
  }
});

/** 重置表单 */
function resetForm() {
  form.value = {
    taskId: null,
    taskName: '',
    cronExpression: '',
    timezone: 'Asia/Shanghai',
    scheduleStatus: 'ENABLED',
    maxRetryCount: 3,
    timeoutSeconds: 3600,
    dependenciesConfig: '',
    alertConfig: '',
    scheduleParams: ''
  };
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
