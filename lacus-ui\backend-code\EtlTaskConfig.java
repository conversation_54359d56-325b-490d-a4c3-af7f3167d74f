package com.lacus.common.core.domain.model;

import lombok.Data;
import java.util.List;

/**
 * ETL任务配置模型
 * 
 * <AUTHOR>
 */
@Data
public class EtlTaskConfig {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 源数据层级
     */
    private String sourceLayer;
    
    /**
     * 目标数据层级
     */
    private String targetLayer;
    
    /**
     * 目标表名
     */
    private String targetTable;
    
    /**
     * 写入模式
     */
    private String writeMode;
    
    /**
     * 源表配置列表
     */
    private List<SourceTable> sourceTables;
    
    /**
     * 字段映射配置列表
     */
    private List<FieldMapping> fieldMappings;
    
    /**
     * 数据质量规则列表
     */
    private List<QualityRule> qualityRules;
}

/**
 * 源表配置
 */
@Data
class SourceTable {
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 表别名
     */
    private String alias;
    
    /**
     * WHERE条件
     */
    private String whereCondition;
    
    /**
     * 是否为主表
     */
    private Boolean isMainTable = false;
}

/**
 * 字段映射配置
 */
@Data
class FieldMapping {
    
    /**
     * 目标字段名
     */
    private String targetField;
    
    /**
     * 字段类型
     */
    private String fieldType;
    
    /**
     * 转换表达式
     */
    private String expression;
    
    /**
     * 默认值
     */
    private String defaultValue;
    
    /**
     * 是否为主键
     */
    private Boolean isPrimaryKey = false;
    
    /**
     * 字段描述
     */
    private String description;
}

/**
 * 数据质量规则
 */
@Data
class QualityRule {
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 规则类型
     */
    private String ruleType;
    
    /**
     * 检查字段
     */
    private String checkField;
    
    /**
     * 阈值
     */
    private Double threshold;
    
    /**
     * 规则配置（JSON格式）
     */
    private String ruleConfig;
    
    /**
     * 严重级别
     */
    private String severity = "WARNING";
}
