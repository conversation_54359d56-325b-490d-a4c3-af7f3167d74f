user  root;
worker_processes  1;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen       8080;
        server_name  localhost;

        # 设置根目录为 /frontend/dist
        location / {
            root   /frontend/dist;  # 更新为正确的路径
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;  # 如果文件不存在，返回 index.html
        }

        # 处理重定向
        location @router {
            rewrite ^.*$ /index.html last;  # 确保重定向到 index.html
        }

        # 处理 API 请求
        location ~ ^/prod-api/ {
            proxy_set_header        Host $host;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        REMOTE-HOST $remote_addr;

            rewrite ^/prod-api/(.*)$ /$1 break;  # 重写 API 请求
            proxy_pass http://lacus_backend:8090;  # 使用后端服务的容器名称
        }

        # 处理静态文件的访问权限
        location /frontend/dist/ {
            autoindex on;  # 启用目录列表（可选）
            allow all;     # 允许所有访问
        }
    }
}