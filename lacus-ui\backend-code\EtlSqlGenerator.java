package com.lacus.service.datawarehouse.impl;

import com.lacus.common.core.domain.model.EtlTaskConfig;
import com.lacus.common.core.domain.model.FieldMapping;
import com.lacus.common.core.domain.model.SourceTable;
import com.lacus.common.enums.WriteMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ETL SQL生成器
 * 根据ETL任务配置生成对应的Doris SQL语句
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class EtlSqlGenerator {
    
    /**
     * 生成SQL语句列表
     * 
     * @param config ETL任务配置
     * @return SQL语句列表
     */
    public List<String> generateSql(EtlTaskConfig config) {
        List<String> sqlStatements = new ArrayList<>();
        
        try {
            // 根据写入模式生成不同的SQL
            WriteMode writeMode = WriteMode.valueOf(config.getWriteMode());
            
            switch (writeMode) {
                case AUTO_CREATE:
                    // 自动建表模式：先建表，再插入数据
                    sqlStatements.add(generateCreateTableSql(config));
                    sqlStatements.add(generateInsertSql(config));
                    break;
                    
                case OVERWRITE:
                    // 覆盖模式：先清空表，再插入数据
                    sqlStatements.add(generateTruncateTableSql(config));
                    sqlStatements.add(generateInsertSql(config));
                    break;
                    
                case APPEND:
                    // 追加模式：直接插入数据
                    sqlStatements.add(generateInsertSql(config));
                    break;
                    
                case UPSERT:
                    // 更新插入模式：使用INSERT INTO ... ON DUPLICATE KEY UPDATE
                    sqlStatements.add(generateUpsertSql(config));
                    break;
                    
                default:
                    throw new IllegalArgumentException("不支持的写入模式: " + writeMode);
            }
            
            log.info("生成SQL成功，任务: {}, 语句数量: {}", config.getTaskName(), sqlStatements.size());
            return sqlStatements;
            
        } catch (Exception e) {
            log.error("生成SQL失败，任务: {}", config.getTaskName(), e);
            throw new RuntimeException("生成SQL失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成建表SQL
     */
    private String generateCreateTableSql(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        
        sql.append("CREATE TABLE IF NOT EXISTS ");
        sql.append(getTargetDatabase(config.getTargetLayer())).append(".");
        sql.append(config.getTargetTable()).append(" (\n");
        
        // 根据字段映射生成表结构
        List<FieldMapping> fieldMappings = config.getFieldMappings();
        for (int i = 0; i < fieldMappings.size(); i++) {
            FieldMapping mapping = fieldMappings.get(i);
            sql.append("  `").append(mapping.getTargetField()).append("` ");
            sql.append(convertToDorisType(mapping.getFieldType()));
            
            // 添加默认值
            if (StringUtils.hasText(mapping.getDefaultValue())) {
                sql.append(" DEFAULT '").append(mapping.getDefaultValue()).append("'");
            }
            
            if (i < fieldMappings.size() - 1) {
                sql.append(",");
            }
            sql.append("\n");
        }
        
        sql.append(") ENGINE=OLAP\n");
        
        // 分布式配置
        String primaryKey = getPrimaryKey(config);
        sql.append("DISTRIBUTED BY HASH(`").append(primaryKey).append("`) BUCKETS 10\n");
        
        // 表属性
        sql.append("PROPERTIES (\n");
        sql.append("  \"replication_num\" = \"1\",\n");
        sql.append("  \"storage_format\" = \"V2\"\n");
        sql.append(");");
        
        return sql.toString();
    }
    
    /**
     * 生成清空表SQL
     */
    private String generateTruncateTableSql(EtlTaskConfig config) {
        return "TRUNCATE TABLE " + getTargetDatabase(config.getTargetLayer()) + "." + config.getTargetTable() + ";";
    }
    
    /**
     * 生成插入SQL
     */
    private String generateInsertSql(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        
        // INSERT INTO 目标表
        sql.append("INSERT INTO ");
        sql.append(getTargetDatabase(config.getTargetLayer())).append(".");
        sql.append(config.getTargetTable()).append(" (\n");
        
        // 目标字段列表
        List<String> targetFields = config.getFieldMappings().stream()
            .map(mapping -> "`" + mapping.getTargetField() + "`")
            .collect(Collectors.toList());
        sql.append("  ").append(String.join(", ", targetFields)).append("\n");
        sql.append(")\n");
        
        // SELECT 子句
        sql.append(generateSelectClause(config));
        
        return sql.toString();
    }
    
    /**
     * 生成UPSERT SQL
     */
    private String generateUpsertSql(EtlTaskConfig config) {
        // Doris支持INSERT INTO ... ON DUPLICATE KEY UPDATE语法
        StringBuilder sql = new StringBuilder();
        
        sql.append(generateInsertSql(config));
        sql.append("\nON DUPLICATE KEY UPDATE\n");
        
        // 更新字段（除主键外的所有字段）
        List<String> updateFields = config.getFieldMappings().stream()
            .filter(mapping -> !isPrimaryKeyField(mapping.getTargetField(), config))
            .map(mapping -> "`" + mapping.getTargetField() + "` = VALUES(`" + mapping.getTargetField() + "`)")
            .collect(Collectors.toList());
            
        sql.append("  ").append(String.join(",\n  ", updateFields));
        
        return sql.toString();
    }
    
    /**
     * 生成SELECT子句
     */
    private String generateSelectClause(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT\n");
        
        // 字段映射
        List<FieldMapping> fieldMappings = config.getFieldMappings();
        for (int i = 0; i < fieldMappings.size(); i++) {
            FieldMapping mapping = fieldMappings.get(i);
            sql.append("  ").append(mapping.getExpression());
            sql.append(" AS `").append(mapping.getTargetField()).append("`");
            if (i < fieldMappings.size() - 1) {
                sql.append(",");
            }
            sql.append("\n");
        }
        
        // FROM 子句
        sql.append(generateFromClause(config));
        
        return sql.toString();
    }
    
    /**
     * 生成FROM子句
     */
    private String generateFromClause(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        List<SourceTable> sourceTables = config.getSourceTables();
        
        if (sourceTables.size() == 1) {
            // 单表查询
            SourceTable table = sourceTables.get(0);
            sql.append("FROM ");
            sql.append(getSourceDatabase(config.getSourceLayer())).append(".");
            sql.append(table.getTableName());
            
            if (StringUtils.hasText(table.getAlias())) {
                sql.append(" ").append(table.getAlias());
            }
            
            if (StringUtils.hasText(table.getWhereCondition())) {
                sql.append("\nWHERE ").append(table.getWhereCondition());
            }
        } else {
            // 多表关联查询
            sql.append("FROM ");
            sql.append(generateMultiTableJoin(config));
        }
        
        return sql.toString();
    }
    
    /**
     * 生成多表关联
     */
    private String generateMultiTableJoin(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        List<SourceTable> sourceTables = config.getSourceTables();
        
        // 主表（第一个表）
        SourceTable mainTable = sourceTables.get(0);
        sql.append(getSourceDatabase(config.getSourceLayer())).append(".");
        sql.append(mainTable.getTableName());
        if (StringUtils.hasText(mainTable.getAlias())) {
            sql.append(" ").append(mainTable.getAlias());
        }
        
        // 关联表
        for (int i = 1; i < sourceTables.size(); i++) {
            SourceTable table = sourceTables.get(i);
            sql.append("\nLEFT JOIN ");
            sql.append(getSourceDatabase(config.getSourceLayer())).append(".");
            sql.append(table.getTableName());
            if (StringUtils.hasText(table.getAlias())) {
                sql.append(" ").append(table.getAlias());
            }
            
            // JOIN条件（这里使用约定：通过同名字段关联，通常是id字段）
            sql.append(" ON ").append(generateJoinCondition(mainTable, table));
        }
        
        // WHERE条件
        List<String> whereConditions = sourceTables.stream()
            .filter(t -> StringUtils.hasText(t.getWhereCondition()))
            .map(t -> "(" + t.getWhereCondition() + ")")
            .collect(Collectors.toList());
            
        if (!whereConditions.isEmpty()) {
            sql.append("\nWHERE ").append(String.join(" AND ", whereConditions));
        }
        
        return sql.toString();
    }
    
    /**
     * 生成JOIN条件
     * 约定：通过同名的id字段进行关联
     */
    private String generateJoinCondition(SourceTable mainTable, SourceTable joinTable) {
        String mainAlias = StringUtils.hasText(mainTable.getAlias()) ? mainTable.getAlias() : mainTable.getTableName();
        String joinAlias = StringUtils.hasText(joinTable.getAlias()) ? joinTable.getAlias() : joinTable.getTableName();
        
        // 默认通过user_id字段关联，实际项目中可以通过配置指定
        return mainAlias + ".user_id = " + joinAlias + ".user_id";
    }
    
    /**
     * 转换为Doris数据类型
     */
    private String convertToDorisType(String fieldType) {
        switch (fieldType.toUpperCase()) {
            case "STRING":
                return "VARCHAR(255)";
            case "INT":
                return "INT";
            case "BIGINT":
                return "BIGINT";
            case "DECIMAL":
                return "DECIMAL(10,2)";
            case "DATE":
                return "DATE";
            case "DATETIME":
                return "DATETIME";
            case "BOOLEAN":
                return "BOOLEAN";
            default:
                return "VARCHAR(255)";
        }
    }
    
    /**
     * 获取主键字段
     */
    private String getPrimaryKey(EtlTaskConfig config) {
        // 默认使用第一个字段作为主键，实际项目中可以通过配置指定
        if (!config.getFieldMappings().isEmpty()) {
            return config.getFieldMappings().get(0).getTargetField();
        }
        return "id";
    }
    
    /**
     * 判断是否为主键字段
     */
    private boolean isPrimaryKeyField(String fieldName, EtlTaskConfig config) {
        return fieldName.equals(getPrimaryKey(config));
    }
    
    /**
     * 获取源数据库名
     */
    private String getSourceDatabase(String layer) {
        return layer.toLowerCase() + "_db";
    }
    
    /**
     * 获取目标数据库名
     */
    private String getTargetDatabase(String layer) {
        return layer.toLowerCase() + "_db";
    }
}
