package com.lacus.service.datawarehouse.impl;

import com.lacus.dao.datawarehouse.entity.EtlTask;
import com.lacus.dao.datawarehouse.entity.EtlExecutionRecord;
import com.lacus.dao.datawarehouse.mapper.EtlTaskMapper;
import com.lacus.dao.datawarehouse.mapper.EtlExecutionRecordMapper;
import com.lacus.service.datawarehouse.IEtlExecutionService;
import com.lacus.service.datawarehouse.IDataQualityService;
import com.lacus.common.exception.BusinessException;
import com.lacus.common.core.domain.model.EtlTaskConfig;
import com.lacus.common.core.domain.model.EtlExecutionResult;
import com.lacus.common.enums.ExecutionStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * ETL任务执行服务实现
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class EtlExecutionServiceImpl implements IEtlExecutionService {
    
    @Autowired
    private EtlTaskMapper etlTaskMapper;
    
    @Autowired
    private EtlExecutionRecordMapper executionRecordMapper;
    
    @Autowired
    private DorisExecutionEngine executionEngine;
    
    @Autowired
    private EtlSqlGenerator sqlGenerator;
    
    @Autowired
    private IDataQualityService qualityService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 提交ETL任务执行
     * 
     * @param taskId 任务ID
     * @return 执行结果
     */
    @Override
    @Async("etlExecutor")
    public CompletableFuture<EtlExecutionResult> submitTask(Long taskId) {
        return CompletableFuture.supplyAsync(() -> {
            EtlExecutionRecord record = null;
            try {
                log.info("开始执行ETL任务: taskId={}", taskId);
                
                // 1. 获取ETL任务配置
                EtlTask task = etlTaskMapper.selectById(taskId);
                if (task == null) {
                    throw new BusinessException("ETL任务不存在: " + taskId);
                }
                
                // 2. 创建执行记录
                record = createExecutionRecord(task);
                
                // 3. 解析任务配置
                EtlTaskConfig config = parseTaskConfig(task);
                
                // 4. 验证配置
                validateConfig(config);
                
                // 5. 生成SQL语句
                List<String> sqlStatements = sqlGenerator.generateSql(config);
                log.info("生成SQL语句数量: {}", sqlStatements.size());
                
                // 6. 执行ETL任务
                EtlExecutionResult result = executionEngine.executeStatements(record, sqlStatements);
                
                // 7. 执行数据质量检查
                if (config.getQualityRules() != null && !config.getQualityRules().isEmpty()) {
                    executeQualityCheck(record, config);
                }
                
                // 8. 更新执行记录
                updateExecutionRecord(record, result, ExecutionStatus.SUCCESS, null);
                
                // 9. 更新任务最后执行时间
                updateTaskLastRunTime(taskId);
                
                log.info("ETL任务执行成功: taskId={}, processedRows={}", taskId, result.getProcessedRows());
                return result;
                
            } catch (Exception e) {
                log.error("ETL任务执行失败: taskId={}", taskId, e);
                if (record != null) {
                    updateExecutionRecord(record, null, ExecutionStatus.FAILED, e.getMessage());
                }
                throw new BusinessException("ETL任务执行失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 创建执行记录
     */
    private EtlExecutionRecord createExecutionRecord(EtlTask task) {
        EtlExecutionRecord record = new EtlExecutionRecord();
        record.setTaskId(task.getId());
        record.setTaskName(task.getTaskName());
        record.setExecutionStatus(ExecutionStatus.RUNNING.name());
        record.setStartTime(LocalDateTime.now());
        record.setCreateTime(LocalDateTime.now());
        
        executionRecordMapper.insert(record);
        return record;
    }
    
    /**
     * 解析任务配置
     */
    private EtlTaskConfig parseTaskConfig(EtlTask task) {
        try {
            EtlTaskConfig config = new EtlTaskConfig();
            config.setTaskId(task.getId());
            config.setTaskName(task.getTaskName());
            config.setSourceLayer(task.getSourceLayer());
            config.setTargetLayer(task.getTargetLayer());
            config.setTargetTable(task.getTargetTable());
            config.setWriteMode(task.getWriteMode());
            
            // 解析JSON配置
            if (task.getSourceTablesConfig() != null) {
                config.setSourceTables(objectMapper.readValue(
                    task.getSourceTablesConfig(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, SourceTable.class)
                ));
            }
            
            if (task.getFieldMappingsConfig() != null) {
                config.setFieldMappings(objectMapper.readValue(
                    task.getFieldMappingsConfig(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, FieldMapping.class)
                ));
            }
            
            if (task.getQualityRulesConfig() != null) {
                config.setQualityRules(objectMapper.readValue(
                    task.getQualityRulesConfig(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, QualityRule.class)
                ));
            }
            
            return config;
        } catch (Exception e) {
            throw new BusinessException("解析ETL任务配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证配置
     */
    private void validateConfig(EtlTaskConfig config) {
        if (config.getSourceTables() == null || config.getSourceTables().isEmpty()) {
            throw new BusinessException("源表配置不能为空");
        }
        
        if (config.getFieldMappings() == null || config.getFieldMappings().isEmpty()) {
            throw new BusinessException("字段映射配置不能为空");
        }
        
        if (config.getTargetTable() == null || config.getTargetTable().trim().isEmpty()) {
            throw new BusinessException("目标表不能为空");
        }
    }
    
    /**
     * 执行数据质量检查
     */
    private void executeQualityCheck(EtlExecutionRecord record, EtlTaskConfig config) {
        try {
            log.info("开始执行数据质量检查: taskId={}", config.getTaskId());
            qualityService.executeQualityCheck(config.getTargetTable(), config.getQualityRules());
            log.info("数据质量检查完成: taskId={}", config.getTaskId());
        } catch (Exception e) {
            log.warn("数据质量检查失败: taskId={}, error={}", config.getTaskId(), e.getMessage());
            // 质量检查失败不影响ETL任务执行
        }
    }
    
    /**
     * 更新执行记录
     */
    @Transactional
    private void updateExecutionRecord(EtlExecutionRecord record, EtlExecutionResult result, 
                                     ExecutionStatus status, String errorMessage) {
        record.setExecutionStatus(status.name());
        record.setEndTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());
        
        if (result != null) {
            record.setProcessedRows(result.getProcessedRows());
            record.setExecutionTime(result.getExecutionTimeMs());
        }
        
        if (errorMessage != null) {
            record.setErrorMessage(errorMessage);
        }
        
        executionRecordMapper.updateById(record);
    }
    
    /**
     * 更新任务最后执行时间
     */
    private void updateTaskLastRunTime(Long taskId) {
        EtlTask task = new EtlTask();
        task.setId(taskId);
        task.setLastRunTime(LocalDateTime.now());
        task.setUpdateTime(LocalDateTime.now());
        etlTaskMapper.updateById(task);
    }
    
    /**
     * 停止ETL任务
     */
    @Override
    public void stopTask(Long taskId) {
        // 这里可以实现任务停止逻辑
        // 比如设置停止标志，中断正在执行的SQL等
        log.info("停止ETL任务: taskId={}", taskId);
        
        // 更新执行记录状态为已停止
        EtlExecutionRecord record = executionRecordMapper.selectLatestByTaskId(taskId);
        if (record != null && ExecutionStatus.RUNNING.name().equals(record.getExecutionStatus())) {
            updateExecutionRecord(record, null, ExecutionStatus.STOPPED, "用户手动停止");
        }
    }
    
    /**
     * 获取执行历史
     */
    @Override
    public List<EtlExecutionRecord> getExecutionHistory(Long taskId) {
        return executionRecordMapper.selectByTaskId(taskId);
    }
    
    /**
     * 获取执行日志
     */
    @Override
    public String getExecutionLog(Long executionId) {
        EtlExecutionRecord record = executionRecordMapper.selectById(executionId);
        return record != null ? record.getExecutionLog() : "";
    }
}
