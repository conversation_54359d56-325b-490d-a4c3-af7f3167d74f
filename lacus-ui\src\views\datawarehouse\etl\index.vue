<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="任务名称" prop="taskName">
        <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="源层级" prop="sourceLayer">
        <el-select v-model="queryParams.sourceLayer" placeholder="请选择源层级" clearable>
          <el-option label="ODS" value="ODS"/>
          <el-option label="DWD" value="DWD"/>
          <el-option label="DWS" value="DWS"/>
        </el-select>
      </el-form-item>
      <el-form-item label="目标层级" prop="targetLayer">
        <el-select v-model="queryParams.targetLayer" placeholder="请选择目标层级" clearable>
          <el-option label="DWD" value="DWD"/>
          <el-option label="DWS" value="DWS"/>
          <el-option label="ADS" value="ADS"/>
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1"/>
          <el-option label="禁用" value="0"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增ETL任务</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="VideoPlay" @click="handleBatchRun" :disabled="multiple">批量执行</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" @click="handleBatchDelete" :disabled="multiple">批量删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="etlList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true"/>
      <el-table-column label="任务描述" align="center" prop="description" :show-overflow-tooltip="true"/>
      <el-table-column label="数据流向" align="center" width="120">
        <template #default="scope">
          <el-tag type="info" size="small">{{ scope.row.sourceLayer }}</el-tag>
          <el-icon style="margin: 0 5px;"><Right/></el-icon>
          <el-tag type="success" size="small">{{ scope.row.targetLayer }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="源表数量" align="center" prop="sourceTableCount" width="100"/>
      <el-table-column label="目标表" align="center" prop="targetTable" :show-overflow-tooltip="true"/>
      <el-table-column label="调度方式" align="center" prop="scheduleType" width="100">
        <template #default="scope">
          <dict-tag :options="schedule_type" :value="scope.row.scheduleType"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="最后执行时间" align="center" prop="lastRunTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastRunTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button-group>
            <el-tooltip content="查看详情" placement="top">
              <el-button type="primary" icon="View" @click="handleDetail(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="编辑配置" placement="top">
              <el-button type="warning" icon="Edit" @click="handleUpdate(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="执行任务" placement="top">
              <el-button type="success" icon="VideoPlay" @click="handleRun(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="查看日志" placement="top">
              <el-button type="info" icon="Document" @click="handleLog(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="danger" icon="Delete" @click="handleDelete(scope.row)" size="small"/>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/修改对话框 -->
    <el-dialog :title="title" v-model="open" width="1200px" append-to-body>
      <el-form ref="etlRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <el-card class="mb-4">
          <template #header>基本信息</template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="任务名称" prop="taskName">
                <el-input v-model="form.taskName" placeholder="请输入任务名称"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="任务描述" prop="description">
                <el-input v-model="form.description" placeholder="请输入任务描述"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="源层级" prop="sourceLayer">
                <el-select v-model="form.sourceLayer" placeholder="请选择源层级" @change="handleSourceLayerChange">
                  <el-option label="ODS" value="ODS"/>
                  <el-option label="DWD" value="DWD"/>
                  <el-option label="DWS" value="DWS"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="目标层级" prop="targetLayer">
                <el-select v-model="form.targetLayer" placeholder="请选择目标层级">
                  <el-option v-for="layer in targetLayerOptions" :key="layer" :label="layer" :value="layer"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="调度方式" prop="scheduleType">
                <el-select v-model="form.scheduleType" placeholder="请选择调度方式">
                  <el-option label="手动执行" value="MANUAL"/>
                  <el-option label="定时调度" value="CRON"/>
                  <el-option label="实时处理" value="REALTIME"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 数据源配置 -->
        <el-card class="mb-4">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>数据源配置</span>
              <el-button type="primary" size="small" @click="handleAddSourceTable">添加源表</el-button>
            </div>
          </template>
          <el-table :data="form.sourceTables" style="width: 100%" max-height="200">
            <el-table-column label="序号" type="index" width="60"/>
            <el-table-column label="源表名" prop="tableName" width="200">
              <template #default="scope">
                <el-select v-model="scope.row.tableName" placeholder="请选择源表" filterable>
                  <el-option
                    v-for="table in sourceTableOptions"
                    :key="table.tableName"
                    :label="table.tableName"
                    :value="table.tableName"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="表别名" prop="alias" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.alias" placeholder="请输入表别名"/>
              </template>
            </el-table-column>
            <el-table-column label="过滤条件" prop="whereCondition">
              <template #default="scope">
                <el-input v-model="scope.row.whereCondition" placeholder="请输入WHERE条件"/>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleRemoveSourceTable(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 目标表配置 -->
        <el-card class="mb-4">
          <template #header>目标表配置</template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="目标表名" prop="targetTable">
                <el-input v-model="form.targetTable" placeholder="请输入目标表名"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="写入模式" prop="writeMode">
                <el-select v-model="form.writeMode" placeholder="请选择写入模式">
                  <el-option label="覆盖写入" value="OVERWRITE"/>
                  <el-option label="追加写入" value="APPEND"/>
                  <el-option label="更新插入" value="UPSERT"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 字段映射配置 -->
        <el-card class="mb-4">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>字段映射配置</span>
              <div>
                <el-button type="success" size="small" @click="handleAutoMapping">智能映射</el-button>
                <el-button type="primary" size="small" @click="handleAddFieldMapping">添加字段</el-button>
              </div>
            </div>
          </template>
          <el-table :data="form.fieldMappings" style="width: 100%" max-height="200">
            <el-table-column label="序号" type="index" width="60"/>
            <el-table-column label="目标字段" prop="targetField" width="150">
              <template #default="scope">
                <el-input v-model="scope.row.targetField" placeholder="目标字段名"/>
              </template>
            </el-table-column>
            <el-table-column label="字段类型" prop="fieldType" width="120">
              <template #default="scope">
                <el-select v-model="scope.row.fieldType" placeholder="类型">
                  <el-option label="STRING" value="STRING"/>
                  <el-option label="INT" value="INT"/>
                  <el-option label="BIGINT" value="BIGINT"/>
                  <el-option label="DECIMAL" value="DECIMAL"/>
                  <el-option label="DATE" value="DATE"/>
                  <el-option label="DATETIME" value="DATETIME"/>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="转换表达式" prop="expression">
              <template #default="scope">
                <el-input v-model="scope.row.expression" placeholder="转换表达式"/>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleRemoveFieldMapping(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="EtlTask">
import { listEtlTask, getEtlTask, addEtlTask, updateEtlTask, deleteEtlTask, updateEtlTaskStatus, runEtlTask, getFieldMappingSuggestions } from '@/api/datawarehouse/etl';
import { getTablesByLayer } from '@/api/metadata/table';

const { proxy } = getCurrentInstance();
const { schedule_type } = proxy.useDict('schedule_type');
const router = useRouter();

const etlList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const sourceTableOptions = ref([]);

const targetLayerOptions = computed(() => {
  const layerMap = {
    'ODS': ['DWD'],
    'DWD': ['DWS'],
    'DWS': ['ADS']
  };
  return layerMap[form.value.sourceLayer] || [];
});

const data = reactive({
  form: {
    taskName: '',
    description: '',
    sourceLayer: 'ODS',
    targetLayer: 'DWD',
    scheduleType: 'MANUAL',
    targetTable: '',
    writeMode: 'OVERWRITE',
    sourceTables: [],
    fieldMappings: []
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    sourceLayer: undefined,
    targetLayer: undefined,
    status: undefined
  },
  rules: {
    taskName: [
      { required: true, message: "任务名称不能为空", trigger: "blur" }
    ],
    sourceLayer: [
      { required: true, message: "源层级不能为空", trigger: "change" }
    ],
    targetLayer: [
      { required: true, message: "目标层级不能为空", trigger: "change" }
    ],
    targetTable: [
      { required: true, message: "目标表不能为空", trigger: "blur" }
    ],
    scheduleType: [
      { required: true, message: "调度方式不能为空", trigger: "change" }
    ],
    writeMode: [
      { required: true, message: "写入模式不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询ETL任务列表 */
function getList() {
  loading.value = true;
  listEtlTask(queryParams.value).then(response => {
    etlList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    taskName: '',
    description: '',
    sourceLayer: 'ODS',
    targetLayer: 'DWD',
    scheduleType: 'MANUAL',
    targetTable: '',
    writeMode: 'OVERWRITE',
    sourceTables: [],
    fieldMappings: []
  };
  proxy.resetForm("etlRef");
}

/** 源层级变化处理 */
function handleSourceLayerChange() {
  form.value.targetLayer = targetLayerOptions.value[0] || '';
  loadSourceTables();
}

/** 加载源表列表 */
function loadSourceTables() {
  if (form.value.sourceLayer) {
    getTablesByLayer(form.value.sourceLayer).then(response => {
      sourceTableOptions.value = response.data || [];
    });
  }
}

/** 添加源表 */
function handleAddSourceTable() {
  form.value.sourceTables.push({
    tableName: '',
    alias: '',
    whereCondition: ''
  });
}

/** 删除源表 */
function handleRemoveSourceTable(index) {
  form.value.sourceTables.splice(index, 1);
}

/** 添加字段映射 */
function handleAddFieldMapping() {
  form.value.fieldMappings.push({
    targetField: '',
    fieldType: 'STRING',
    expression: '',
    defaultValue: ''
  });
}

/** 删除字段映射 */
function handleRemoveFieldMapping(index) {
  form.value.fieldMappings.splice(index, 1);
}

/** 智能映射 */
function handleAutoMapping() {
  if (form.value.sourceTables.length === 0) {
    proxy.$modal.msgWarning('请先添加源表');
    return;
  }

  const sourceTables = form.value.sourceTables.map(t => t.tableName).filter(Boolean);
  if (sourceTables.length === 0) {
    proxy.$modal.msgWarning('请先选择源表');
    return;
  }

  getFieldMappingSuggestions(form.value.sourceLayer, form.value.targetLayer, sourceTables)
    .then(response => {
      form.value.fieldMappings = response.data || [];
      proxy.$modal.msgSuccess('智能映射完成');
    })
    .catch(() => {
      proxy.$modal.msgError('智能映射失败');
    });
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  loadSourceTables();
  open.value = true;
  title.value = "添加ETL任务";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const taskId = row.id || ids.value;
  getEtlTask(taskId).then(response => {
    form.value = response.data;
    // 确保数组字段不为空
    if (!form.value.sourceTables) form.value.sourceTables = [];
    if (!form.value.fieldMappings) form.value.fieldMappings = [];

    loadSourceTables();
    open.value = true;
    title.value = "修改ETL任务";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["etlRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateEtlTask(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addEtlTask(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 查看详情 */
function handleDetail(row) {
  router.push(`/datawarehouse/etl/detail/${row.id}`);
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? "启用" : "停用";
  proxy.$modal.confirm(`确认要"${text}""${row.taskName}"任务吗？`).then(() => {
    return updateEtlTaskStatus(row.id, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(() => {
    row.status = row.status === 0 ? 1 : 0;
  });
}

/** 执行任务 */
function handleRun(row) {
  proxy.$modal.confirm(`确认要执行"${row.taskName}"任务吗？`).then(() => {
    return runEtlTask(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("任务执行成功");
    getList();
  });
}

/** 查看日志 */
function handleLog(row) {
  router.push(`/datawarehouse/etl/log/${row.id}`);
}

/** 删除按钮操作 */
function handleDelete(row) {
  const taskIds = row.id || ids.value;
  proxy.$modal.confirm(`是否确认删除ETL任务编号为"${taskIds}"的数据项？`).then(() => {
    return deleteEtlTask(taskIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  });
}

/** 批量删除 */
function handleBatchDelete() {
  handleDelete();
}

/** 批量执行 */
function handleBatchRun() {
  proxy.$modal.confirm(`确认要批量执行选中的${ids.value.length}个任务吗？`).then(() => {
    return runEtlTask(ids.value.join(','));
  }).then(() => {
    proxy.$modal.msgSuccess("批量执行成功");
    getList();
  });
}

getList();
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}

.el-card {
  margin-bottom: 16px;
}

.el-card:last-child {
  margin-bottom: 0;
}
</style>
