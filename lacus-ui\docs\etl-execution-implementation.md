# ETL任务执行实现详解

## 🎯 **核心设计思路**

### **执行流程**
1. **任务解析** → 解析ETL任务配置
2. **SQL生成** → 根据配置生成Doris SQL
3. **执行引擎** → 通过JDBC执行SQL
4. **监控记录** → 记录执行状态和指标
5. **质量检查** → 执行数据质量规则

### **技术架构**
```
前端ETL配置 → 后端解析 → SQL生成 → Doris执行 → 结果监控
```

## 📋 **详细实现**

### **1. EtlExecutionServiceImpl.submitTask() 方法**

```java
@Service
@Slf4j
public class EtlExecutionServiceImpl implements EtlExecutionService {
    
    @Autowired
    private EtlTaskMapper etlTaskMapper;
    
    @Autowired
    private EtlExecutionRecordMapper executionRecordMapper;
    
    @Autowired
    private DorisJdbcTemplate dorisJdbcTemplate;
    
    @Autowired
    private EtlSqlGenerator sqlGenerator;
    
    @Autowired
    private DataQualityService qualityService;
    
    @Override
    @Async("etlExecutor")
    public CompletableFuture<EtlExecutionResult> submitTask(Long taskId) {
        return CompletableFuture.supplyAsync(() -> {
            EtlExecutionRecord record = null;
            try {
                // 1. 获取ETL任务配置
                EtlTask task = etlTaskMapper.selectById(taskId);
                if (task == null) {
                    throw new BusinessException("ETL任务不存在: " + taskId);
                }
                
                // 2. 创建执行记录
                record = createExecutionRecord(task);
                
                // 3. 解析任务配置
                EtlTaskConfig config = parseTaskConfig(task);
                
                // 4. 生成SQL语句
                List<String> sqlStatements = sqlGenerator.generateSql(config);
                
                // 5. 执行ETL任务
                EtlExecutionResult result = executeEtlTask(record, config, sqlStatements);
                
                // 6. 执行数据质量检查
                if (config.getQualityRules() != null && !config.getQualityRules().isEmpty()) {
                    executeQualityCheck(record, config);
                }
                
                // 7. 更新执行记录
                updateExecutionRecord(record, result, ExecutionStatus.SUCCESS);
                
                return result;
                
            } catch (Exception e) {
                log.error("ETL任务执行失败: taskId={}", taskId, e);
                if (record != null) {
                    updateExecutionRecord(record, null, ExecutionStatus.FAILED, e.getMessage());
                }
                throw new BusinessException("ETL任务执行失败: " + e.getMessage());
            }
        });
    }
}
```

### **2. SQL生成器实现**

```java
@Component
@Slf4j
public class EtlSqlGenerator {
    
    public List<String> generateSql(EtlTaskConfig config) {
        List<String> sqlStatements = new ArrayList<>();
        
        // 1. 根据写入模式处理目标表
        switch (config.getWriteMode()) {
            case AUTO_CREATE:
                sqlStatements.add(generateCreateTableSql(config));
                sqlStatements.add(generateInsertSql(config));
                break;
            case OVERWRITE:
                sqlStatements.add(generateTruncateTableSql(config));
                sqlStatements.add(generateInsertSql(config));
                break;
            case APPEND:
                sqlStatements.add(generateInsertSql(config));
                break;
            case UPSERT:
                sqlStatements.add(generateUpsertSql(config));
                break;
        }
        
        return sqlStatements;
    }
    
    private String generateCreateTableSql(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE IF NOT EXISTS ");
        sql.append(getTargetDatabase(config.getTargetLayer())).append(".");
        sql.append(config.getTargetTable()).append(" (\n");
        
        // 根据字段映射生成表结构
        for (int i = 0; i < config.getFieldMappings().size(); i++) {
            FieldMapping mapping = config.getFieldMappings().get(i);
            sql.append("  ").append(mapping.getTargetField()).append(" ");
            sql.append(convertToDorisType(mapping.getFieldType()));
            if (i < config.getFieldMappings().size() - 1) {
                sql.append(",");
            }
            sql.append("\n");
        }
        
        sql.append(") ENGINE=OLAP\n");
        sql.append("DISTRIBUTED BY HASH(").append(getPrimaryKey(config)).append(") BUCKETS 10\n");
        sql.append("PROPERTIES (\n");
        sql.append("  \"replication_num\" = \"1\"\n");
        sql.append(");");
        
        return sql.toString();
    }
    
    private String generateInsertSql(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        
        // INSERT INTO 目标表
        sql.append("INSERT INTO ");
        sql.append(getTargetDatabase(config.getTargetLayer())).append(".");
        sql.append(config.getTargetTable()).append(" (\n");
        
        // 目标字段列表
        List<String> targetFields = config.getFieldMappings().stream()
            .map(FieldMapping::getTargetField)
            .collect(Collectors.toList());
        sql.append("  ").append(String.join(", ", targetFields)).append("\n");
        sql.append(")\n");
        
        // SELECT 子句
        sql.append("SELECT\n");
        for (int i = 0; i < config.getFieldMappings().size(); i++) {
            FieldMapping mapping = config.getFieldMappings().get(i);
            sql.append("  ").append(mapping.getExpression());
            sql.append(" AS ").append(mapping.getTargetField());
            if (i < config.getFieldMappings().size() - 1) {
                sql.append(",");
            }
            sql.append("\n");
        }
        
        // FROM 子句 - 处理多表关联
        sql.append("FROM ");
        List<SourceTable> sourceTables = config.getSourceTables();
        if (sourceTables.size() == 1) {
            // 单表
            SourceTable table = sourceTables.get(0);
            sql.append(getSourceDatabase(config.getSourceLayer())).append(".");
            sql.append(table.getTableName());
            if (StringUtils.hasText(table.getAlias())) {
                sql.append(" ").append(table.getAlias());
            }
            if (StringUtils.hasText(table.getWhereCondition())) {
                sql.append("\nWHERE ").append(table.getWhereCondition());
            }
        } else {
            // 多表关联
            sql.append(generateMultiTableJoin(config));
        }
        
        return sql.toString();
    }
    
    private String generateMultiTableJoin(EtlTaskConfig config) {
        StringBuilder sql = new StringBuilder();
        List<SourceTable> sourceTables = config.getSourceTables();
        
        // 主表
        SourceTable mainTable = sourceTables.get(0);
        sql.append(getSourceDatabase(config.getSourceLayer())).append(".");
        sql.append(mainTable.getTableName()).append(" ").append(mainTable.getAlias());
        
        // 关联表
        for (int i = 1; i < sourceTables.size(); i++) {
            SourceTable table = sourceTables.get(i);
            sql.append("\nLEFT JOIN ");
            sql.append(getSourceDatabase(config.getSourceLayer())).append(".");
            sql.append(table.getTableName()).append(" ").append(table.getAlias());
            
            // 这里需要根据实际业务逻辑生成JOIN条件
            // 可以通过配置或者约定来处理
            sql.append(" ON ").append(generateJoinCondition(mainTable, table));
        }
        
        // WHERE条件
        List<String> whereConditions = sourceTables.stream()
            .filter(t -> StringUtils.hasText(t.getWhereCondition()))
            .map(t -> "(" + t.getWhereCondition() + ")")
            .collect(Collectors.toList());
            
        if (!whereConditions.isEmpty()) {
            sql.append("\nWHERE ").append(String.join(" AND ", whereConditions));
        }
        
        return sql.toString();
    }
    
    private String getSourceDatabase(String layer) {
        return layer.toLowerCase() + "_db";
    }
    
    private String getTargetDatabase(String layer) {
        return layer.toLowerCase() + "_db";
    }
}
```

### **3. 执行引擎实现**

```java
@Component
@Slf4j
public class DorisExecutionEngine {
    
    @Autowired
    private DorisJdbcTemplate jdbcTemplate;
    
    public EtlExecutionResult executeStatements(EtlExecutionRecord record, List<String> sqlStatements) {
        EtlExecutionResult result = new EtlExecutionResult();
        result.setStartTime(LocalDateTime.now());
        
        long totalProcessedRows = 0;
        
        try {
            for (String sql : sqlStatements) {
                log.info("执行SQL: {}", sql);
                
                long startTime = System.currentTimeMillis();
                int affectedRows = jdbcTemplate.update(sql);
                long endTime = System.currentTimeMillis();
                
                totalProcessedRows += affectedRows;
                
                // 记录SQL执行详情
                recordSqlExecution(record.getId(), sql, affectedRows, endTime - startTime);
            }
            
            result.setEndTime(LocalDateTime.now());
            result.setProcessedRows(totalProcessedRows);
            result.setStatus(ExecutionStatus.SUCCESS);
            
        } catch (Exception e) {
            result.setEndTime(LocalDateTime.now());
            result.setStatus(ExecutionStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            throw e;
        }
        
        return result;
    }
}
```

## 🎯 **关键特性**

### **1. 多表关联支持**
- 自动生成LEFT JOIN语句
- 支持复杂的WHERE条件
- 智能字段映射

### **2. 写入模式支持**
- **AUTO_CREATE**: 自动建表 + 插入数据
- **OVERWRITE**: 清空表 + 插入数据  
- **APPEND**: 直接插入数据
- **UPSERT**: 更新插入（基于主键）

### **3. 数据质量集成**
- 执行后自动进行质量检查
- 支持多种质量规则
- 质量问题告警

### **4. 执行监控**
- 详细的执行记录
- 性能指标统计
- 错误日志记录

## 📊 **执行示例**

### **配置示例**
```json
{
  "taskName": "用户画像ETL",
  "sourceLayer": "ODS",
  "targetLayer": "DWS",
  "sourceTables": [
    {"tableName": "ods_user_info", "alias": "u", "whereCondition": "status = 1"},
    {"tableName": "ods_order_summary", "alias": "o", "whereCondition": "order_date >= '2024-01-01'"}
  ],
  "targetTable": "dws_user_profile",
  "writeMode": "AUTO_CREATE",
  "fieldMappings": [
    {"targetField": "user_id", "fieldType": "BIGINT", "expression": "u.user_id"},
    {"targetField": "total_amount", "fieldType": "DECIMAL", "expression": "COALESCE(o.total_amount, 0.00)"}
  ]
}
```

### **生成的SQL**
```sql
-- 1. 创建目标表
CREATE TABLE IF NOT EXISTS dws_db.dws_user_profile (
  user_id BIGINT,
  total_amount DECIMAL(10,2)
) ENGINE=OLAP
DISTRIBUTED BY HASH(user_id) BUCKETS 10;

-- 2. 插入数据
INSERT INTO dws_db.dws_user_profile (user_id, total_amount)
SELECT
  u.user_id AS user_id,
  COALESCE(o.total_amount, 0.00) AS total_amount
FROM ods_db.ods_user_info u
LEFT JOIN ods_db.ods_order_summary o ON u.user_id = o.user_id AND o.order_date >= '2024-01-01'
WHERE u.status = 1;
```

这样的实现方案完全支持您的Doris环境和多源单目标的ETL需求！
