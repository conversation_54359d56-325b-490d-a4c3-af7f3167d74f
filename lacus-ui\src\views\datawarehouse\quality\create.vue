<template>
  <el-dialog 
    :title="isEdit ? '编辑质量监控' : '新增质量监控'" 
    v-model="dialogVisible" 
    width="900px" 
    append-to-body
    @close="handleCancel"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="表名" prop="tableName">
            <el-input v-model="form.tableName" placeholder="请输入表名">
              <template #append>
                <el-button @click="handleGetTableFields">获取字段</el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据层级" prop="dataLayer">
            <el-select v-model="form.dataLayer" placeholder="请选择数据层级" style="width: 100%">
              <el-option label="ODS" value="ODS"/>
              <el-option label="DWD" value="DWD"/>
              <el-option label="DWS" value="DWS"/>
              <el-option label="ADS" value="ADS"/>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="监控名称" prop="monitorName">
            <el-input v-model="form.monitorName" placeholder="请输入监控名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="监控状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="监控描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="2" placeholder="请输入监控描述" />
      </el-form-item>
      
      <!-- 质量规则配置 -->
      <el-form-item label="质量规则">
        <div style="margin-bottom: 10px;">
          <el-button type="primary" @click="handleAddRule" size="small">添加规则</el-button>
          <el-button type="info" @click="handleLoadTemplate" size="small">加载模板</el-button>
        </div>
        
        <el-table :data="form.rules" style="width: 100%" border>
          <el-table-column label="规则名称" prop="ruleName" width="150">
            <template #default="scope">
              <el-input v-model="scope.row.ruleName" placeholder="规则名称" size="small"/>
            </template>
          </el-table-column>
          <el-table-column label="规则类型" prop="ruleType" width="120">
            <template #default="scope">
              <el-select v-model="scope.row.ruleType" placeholder="选择类型" size="small">
                <el-option label="空值检查" value="NULL_CHECK"/>
                <el-option label="重复值检查" value="DUPLICATE_CHECK"/>
                <el-option label="数据范围检查" value="RANGE_CHECK"/>
                <el-option label="格式检查" value="FORMAT_CHECK"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="检查字段" prop="checkField" width="120">
            <template #default="scope">
              <el-select v-model="scope.row.checkField" placeholder="选择字段" size="small">
                <el-option 
                  v-for="field in tableFields" 
                  :key="field.fieldName" 
                  :label="field.fieldName" 
                  :value="field.fieldName"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="阈值" prop="threshold" width="100">
            <template #default="scope">
              <el-input-number v-model="scope.row.threshold" :min="0" size="small" style="width: 100%"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <el-button type="danger" @click="handleRemoveRule(scope.$index)" size="small" icon="Delete"/>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      
      <el-form-item label="阈值配置" prop="thresholdConfig">
        <el-input
          v-model="form.thresholdConfig"
          type="textarea"
          :rows="3"
          placeholder="请输入阈值配置（JSON格式）"
        />
      </el-form-item>
      
      <el-form-item label="告警配置" prop="alertConfig">
        <el-input
          v-model="form.alertConfig"
          type="textarea"
          :rows="3"
          placeholder="请输入告警配置（JSON格式）"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="QualityMonitorCreate">
import { getQualityMonitor, addQualityMonitor, updateQualityMonitor, getTableFields, getQualityRuleTemplates } from '@/api/datawarehouse/quality';

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  monitorId: {
    type: [String, Number],
    default: null
  }
});

// 组件事件
const emit = defineEmits(['update:visible', 'success']);

const { proxy } = getCurrentInstance();

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const isEdit = computed(() => !!props.monitorId);
const saving = ref(false);
const tableFields = ref([]);

const data = reactive({
  form: {
    tableName: '',
    dataLayer: '',
    monitorName: '',
    description: '',
    status: 1,
    rules: [],
    thresholdConfig: '',
    alertConfig: ''
  },
  rules: {
    tableName: [
      { required: true, message: "表名不能为空", trigger: "blur" }
    ],
    dataLayer: [
      { required: true, message: "数据层级不能为空", trigger: "change" }
    ],
    monitorName: [
      { required: true, message: "监控名称不能为空", trigger: "blur" }
    ]
  }
});

const { form, rules } = toRefs(data);

/** 获取表字段 */
function handleGetTableFields() {
  if (!form.value.tableName) {
    proxy.$modal.msgWarning("请先输入表名");
    return;
  }
  
  getTableFields(form.value.tableName).then(response => {
    tableFields.value = response.data || [];
    proxy.$modal.msgSuccess("获取字段成功");
  }).catch(() => {
    proxy.$modal.msgError("获取字段失败，请检查表名是否正确");
  });
}

/** 添加规则 */
function handleAddRule() {
  form.value.rules.push({
    ruleName: '',
    ruleType: '',
    checkField: '',
    threshold: 0
  });
}

/** 移除规则 */
function handleRemoveRule(index) {
  form.value.rules.splice(index, 1);
}

/** 加载模板 */
function handleLoadTemplate() {
  getQualityRuleTemplates().then(response => {
    const templates = response.data || [];
    // 简化实现：直接加载第一个模板
    if (templates.length > 0) {
      form.value.rules = [...templates];
      proxy.$modal.msgSuccess("模板加载成功");
    } else {
      proxy.$modal.msgWarning("暂无可用模板");
    }
  });
}

/** 保存 */
function handleSave() {
  proxy.$refs.formRef.validate(valid => {
    if (valid) {
      saving.value = true;
      const api = isEdit.value ? updateQualityMonitor : addQualityMonitor;
      const data = { 
        ...form.value, 
        rulesConfig: JSON.stringify(form.value.rules)
      };
      if (isEdit.value) {
        data.id = props.monitorId;
      }

      api(data).then(() => {
        proxy.$modal.msgSuccess(isEdit.value ? '修改成功' : '新增成功');
        emit('success');
      }).finally(() => {
        saving.value = false;
      });
    }
  });
}

/** 取消 */
function handleCancel() {
  dialogVisible.value = false;
}

/** 监听弹窗显示状态 */
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
    if (isEdit.value && props.monitorId) {
      getQualityMonitor(props.monitorId).then(response => {
        form.value = response.data;
        // 解析规则配置
        if (form.value.rulesConfig) {
          try {
            form.value.rules = JSON.parse(form.value.rulesConfig);
          } catch (e) {
            form.value.rules = [];
          }
        }
        if (form.value.tableName) {
          handleGetTableFields();
        }
      });
    }
  }
});

/** 重置表单 */
function resetForm() {
  form.value = {
    tableName: '',
    dataLayer: '',
    monitorName: '',
    description: '',
    status: 1,
    rules: [],
    thresholdConfig: '',
    alertConfig: ''
  };
  tableFields.value = [];
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
