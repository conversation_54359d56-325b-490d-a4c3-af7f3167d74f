<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>数据血缘关系</span>
          <div>
            <el-select v-model="selectedTask" placeholder="请选择ETL任务" @change="handleTaskChange" style="width: 300px;">
              <el-option
                v-for="task in etlTasks"
                :key="task.id"
                :label="task.taskName"
                :value="task.id"
              />
            </el-select>
            <el-button type="primary" @click="refreshLineage" :loading="loading">刷新</el-button>
          </div>
        </div>
      </template>

      <!-- 血缘图谱 -->
      <div class="lineage-container">
        <div id="lineage-graph" ref="lineageGraph" style="width: 100%; height: 600px;"></div>
      </div>

      <!-- 图例说明 -->
      <el-card class="legend-card">
        <template #header>图例说明</template>
        <div class="legend-content">
          <div class="legend-item">
            <div class="legend-color ods-color"></div>
            <span>ODS层表</span>
          </div>
          <div class="legend-item">
            <div class="legend-color dwd-color"></div>
            <span>DWD层表</span>
          </div>
          <div class="legend-item">
            <div class="legend-color dws-color"></div>
            <span>DWS层表</span>
          </div>
          <div class="legend-item">
            <div class="legend-color ads-color"></div>
            <span>ADS层表</span>
          </div>
          <div class="legend-item">
            <div class="legend-color etl-color"></div>
            <span>ETL任务</span>
          </div>
        </div>
      </el-card>
    </el-card>

    <!-- 节点详情对话框 -->
    <el-dialog title="节点详情" v-model="nodeDetailVisible" width="60%">
      <el-descriptions :column="2" border v-if="selectedNode">
        <el-descriptions-item label="节点名称">{{ selectedNode.name }}</el-descriptions-item>
        <el-descriptions-item label="节点类型">{{ selectedNode.type }}</el-descriptions-item>
        <el-descriptions-item label="数据层级" v-if="selectedNode.layer">{{ selectedNode.layer }}</el-descriptions-item>
        <el-descriptions-item label="表名" v-if="selectedNode.tableName">{{ selectedNode.tableName }}</el-descriptions-item>
        <el-descriptions-item label="记录数" v-if="selectedNode.recordCount">{{ selectedNode.recordCount }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" v-if="selectedNode.updateTime">{{ parseTime(selectedNode.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="描述" span="2" v-if="selectedNode.description">{{ selectedNode.description }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 字段信息 -->
      <el-card class="mt-4" v-if="selectedNode.fields && selectedNode.fields.length > 0">
        <template #header>字段信息</template>
        <el-table :data="selectedNode.fields" style="width: 100%">
          <el-table-column label="字段名" prop="fieldName"/>
          <el-table-column label="字段类型" prop="fieldType"/>
          <el-table-column label="是否主键" prop="isPrimaryKey">
            <template #default="scope">
              <el-tag v-if="scope.row.isPrimaryKey" type="warning" size="small">主键</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="注释" prop="comment" :show-overflow-tooltip="true"/>
        </el-table>
      </el-card>
    </el-dialog>
  </div>
</template>

<script setup name="DataLineage">
import { listEtlTask, getDataLineage } from '@/api/datawarehouse/etl';
import * as echarts from 'echarts';

const { proxy } = getCurrentInstance();

const etlTasks = ref([]);
const selectedTask = ref('');
const loading = ref(false);
const lineageGraph = ref(null);
const nodeDetailVisible = ref(false);
const selectedNode = ref(null);
let chartInstance = null;

/** 获取ETL任务列表 */
function getEtlTasks() {
  listEtlTask({ pageNum: 1, pageSize: 1000 }).then(response => {
    etlTasks.value = response.rows || [];
    if (etlTasks.value.length > 0) {
      selectedTask.value = etlTasks.value[0].id;
      loadLineageData();
    }
  });
}

/** 任务变化处理 */
function handleTaskChange() {
  if (selectedTask.value) {
    loadLineageData();
  }
}

/** 刷新血缘关系 */
function refreshLineage() {
  if (selectedTask.value) {
    loadLineageData();
  }
}

/** 加载血缘数据 */
function loadLineageData() {
  loading.value = true;
  getDataLineage(selectedTask.value).then(response => {
    renderLineageGraph(response.data);
  }).finally(() => {
    loading.value = false;
  });
}

/** 渲染血缘图谱 */
function renderLineageGraph(data) {
  if (!chartInstance) {
    chartInstance = echarts.init(lineageGraph.value);
  }

  const nodes = data.nodes.map(node => ({
    id: node.id,
    name: node.name,
    category: getNodeCategory(node.type, node.layer),
    symbolSize: getNodeSize(node.type),
    itemStyle: {
      color: getNodeColor(node.type, node.layer)
    },
    label: {
      show: true,
      fontSize: 12
    },
    ...node
  }));

  const links = data.edges.map(edge => ({
    source: edge.source,
    target: edge.target,
    lineStyle: {
      color: '#999',
      width: 2
    }
  }));

  const categories = [
    { name: 'ODS表' },
    { name: 'DWD表' },
    { name: 'DWS表' },
    { name: 'ADS表' },
    { name: 'ETL任务' }
  ];

  const option = {
    title: {
      text: '数据血缘关系图',
      left: 'center',
      textStyle: {
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        if (params.dataType === 'node') {
          return `
            <div>
              <strong>${params.data.name}</strong><br/>
              类型: ${params.data.type}<br/>
              ${params.data.layer ? `层级: ${params.data.layer}<br/>` : ''}
              ${params.data.recordCount ? `记录数: ${params.data.recordCount}<br/>` : ''}
            </div>
          `;
        }
        return '';
      }
    },
    legend: {
      data: categories.map(cat => cat.name),
      bottom: 10
    },
    series: [{
      type: 'graph',
      layout: 'force',
      data: nodes,
      links: links,
      categories: categories,
      roam: true,
      focusNodeAdjacency: true,
      force: {
        repulsion: 1000,
        gravity: 0.1,
        edgeLength: 200
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 4
        }
      }
    }]
  };

  chartInstance.setOption(option);

  // 添加点击事件
  chartInstance.off('click');
  chartInstance.on('click', function(params) {
    if (params.dataType === 'node') {
      selectedNode.value = params.data;
      nodeDetailVisible.value = true;
    }
  });
}

/** 获取节点分类 */
function getNodeCategory(type, layer) {
  if (type === 'table') {
    return `${layer}表`;
  } else if (type === 'etl') {
    return 'ETL任务';
  }
  return '未知';
}

/** 获取节点大小 */
function getNodeSize(type) {
  return type === 'etl' ? 60 : 40;
}

/** 获取节点颜色 */
function getNodeColor(type, layer) {
  if (type === 'table') {
    const colorMap = {
      'ODS': '#67C23A',
      'DWD': '#409EFF',
      'DWS': '#E6A23C',
      'ADS': '#F56C6C'
    };
    return colorMap[layer] || '#909399';
  } else if (type === 'etl') {
    return '#9C27B0';
  }
  return '#909399';
}

onMounted(() => {
  getEtlTasks();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lineage-container {
  margin: 20px 0;
}

.legend-card {
  margin-top: 20px;
}

.legend-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.ods-color {
  background-color: #67C23A;
}

.dwd-color {
  background-color: #409EFF;
}

.dws-color {
  background-color: #E6A23C;
}

.ads-color {
  background-color: #F56C6C;
}

.etl-color {
  background-color: #9C27B0;
}

.mt-4 {
  margin-top: 16px;
}
</style>
