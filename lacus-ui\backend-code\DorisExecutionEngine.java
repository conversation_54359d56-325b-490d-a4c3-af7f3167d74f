package com.lacus.service.datawarehouse.impl;

import com.lacus.dao.datawarehouse.entity.EtlExecutionRecord;
import com.lacus.dao.datawarehouse.entity.EtlSqlExecutionLog;
import com.lacus.dao.datawarehouse.mapper.EtlSqlExecutionLogMapper;
import com.lacus.common.core.domain.model.EtlExecutionResult;
import com.lacus.common.enums.ExecutionStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Doris执行引擎
 * 负责执行SQL语句并记录执行结果
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class DorisExecutionEngine {
    
    @Autowired
    private JdbcTemplate dorisJdbcTemplate;
    
    @Autowired
    private EtlSqlExecutionLogMapper sqlExecutionLogMapper;
    
    /**
     * 执行SQL语句列表
     * 
     * @param record 执行记录
     * @param sqlStatements SQL语句列表
     * @return 执行结果
     */
    public EtlExecutionResult executeStatements(EtlExecutionRecord record, List<String> sqlStatements) {
        EtlExecutionResult result = new EtlExecutionResult();
        result.setStartTime(LocalDateTime.now());
        result.setStatus(ExecutionStatus.RUNNING);
        
        long totalProcessedRows = 0;
        long totalExecutionTime = 0;
        
        try {
            log.info("开始执行SQL语句，数量: {}", sqlStatements.size());
            
            for (int i = 0; i < sqlStatements.size(); i++) {
                String sql = sqlStatements.get(i);
                log.info("执行第{}条SQL: {}", i + 1, sql.substring(0, Math.min(sql.length(), 100)) + "...");
                
                // 执行单条SQL
                SqlExecutionResult sqlResult = executeSingleSql(record.getId(), sql, i + 1);
                
                totalProcessedRows += sqlResult.getAffectedRows();
                totalExecutionTime += sqlResult.getExecutionTime();
                
                // 检查是否需要停止执行
                if (shouldStopExecution(record.getId())) {
                    log.info("检测到停止信号，中断执行");
                    result.setStatus(ExecutionStatus.STOPPED);
                    break;
                }
            }
            
            if (result.getStatus() != ExecutionStatus.STOPPED) {
                result.setStatus(ExecutionStatus.SUCCESS);
            }
            
            result.setEndTime(LocalDateTime.now());
            result.setProcessedRows(totalProcessedRows);
            result.setExecutionTimeMs(totalExecutionTime);
            
            log.info("SQL执行完成，处理行数: {}, 总耗时: {}ms", totalProcessedRows, totalExecutionTime);
            
        } catch (Exception e) {
            log.error("SQL执行失败", e);
            result.setEndTime(LocalDateTime.now());
            result.setStatus(ExecutionStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setExecutionTimeMs(totalExecutionTime);
            throw new RuntimeException("SQL执行失败: " + e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 执行单条SQL
     */
    private SqlExecutionResult executeSingleSql(Long executionRecordId, String sql, int sqlIndex) {
        SqlExecutionResult result = new SqlExecutionResult();
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行SQL
            int affectedRows = dorisJdbcTemplate.update(sql);
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            result.setAffectedRows(affectedRows);
            result.setExecutionTime(executionTime);
            result.setSuccess(true);
            
            // 记录SQL执行日志
            recordSqlExecution(executionRecordId, sql, sqlIndex, affectedRows, executionTime, null);
            
            log.info("SQL执行成功，影响行数: {}, 耗时: {}ms", affectedRows, executionTime);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            result.setAffectedRows(0);
            result.setExecutionTime(executionTime);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            
            // 记录SQL执行错误日志
            recordSqlExecution(executionRecordId, sql, sqlIndex, 0, executionTime, e.getMessage());
            
            log.error("SQL执行失败，耗时: {}ms, 错误: {}", executionTime, e.getMessage());
            throw e;
        }
        
        return result;
    }
    
    /**
     * 记录SQL执行日志
     */
    private void recordSqlExecution(Long executionRecordId, String sql, int sqlIndex, 
                                   int affectedRows, long executionTime, String errorMessage) {
        try {
            EtlSqlExecutionLog log = new EtlSqlExecutionLog();
            log.setExecutionRecordId(executionRecordId);
            log.setSqlContent(sql);
            log.setSqlIndex(sqlIndex);
            log.setAffectedRows(affectedRows);
            log.setExecutionTime(executionTime);
            log.setExecutionStatus(errorMessage == null ? "SUCCESS" : "FAILED");
            log.setErrorMessage(errorMessage);
            log.setCreateTime(LocalDateTime.now());
            
            sqlExecutionLogMapper.insert(log);
        } catch (Exception e) {
            // 记录日志失败不影响主流程
            log.warn("记录SQL执行日志失败", e);
        }
    }
    
    /**
     * 检查是否需要停止执行
     * 这里可以通过检查数据库中的停止标志来实现
     */
    private boolean shouldStopExecution(Long executionRecordId) {
        // 实现停止检查逻辑
        // 可以查询execution_record表中的stop_flag字段
        return false;
    }
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection() {
        try {
            dorisJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            return true;
        } catch (Exception e) {
            log.error("测试Doris连接失败", e);
            return false;
        }
    }
    
    /**
     * 获取表行数
     */
    public long getTableRowCount(String database, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM " + database + "." + tableName;
            return dorisJdbcTemplate.queryForObject(sql, Long.class);
        } catch (Exception e) {
            log.error("获取表行数失败: {}.{}", database, tableName, e);
            return 0;
        }
    }
    
    /**
     * 检查表是否存在
     */
    public boolean tableExists(String database, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                        "WHERE table_schema = ? AND table_name = ?";
            Integer count = dorisJdbcTemplate.queryForObject(sql, Integer.class, database, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表是否存在失败: {}.{}", database, tableName, e);
            return false;
        }
    }
    
    /**
     * SQL执行结果内部类
     */
    private static class SqlExecutionResult {
        private int affectedRows;
        private long executionTime;
        private boolean success;
        private String errorMessage;
        
        // getters and setters
        public int getAffectedRows() { return affectedRows; }
        public void setAffectedRows(int affectedRows) { this.affectedRows = affectedRows; }
        
        public long getExecutionTime() { return executionTime; }
        public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
