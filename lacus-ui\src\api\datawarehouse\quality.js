import request from '@/utils/request'

// 查询数据质量监控列表
export function listQualityMonitor(query) {
  return request({
    url: '/datawarehouse/quality/monitor/list',
    method: 'get',
    params: query
  })
}

// 查询数据质量监控详细
export function getQualityMonitor(id) {
  return request({
    url: '/datawarehouse/quality/monitor/' + id,
    method: 'get'
  })
}

// 新增数据质量监控
export function addQualityMonitor(data) {
  return request({
    url: '/datawarehouse/quality/monitor',
    method: 'post',
    data: data
  })
}

// 修改数据质量监控
export function updateQualityMonitor(data) {
  return request({
    url: '/datawarehouse/quality/monitor',
    method: 'put',
    data: data
  })
}

// 删除数据质量监控
export function deleteQualityMonitor(id) {
  return request({
    url: '/datawarehouse/quality/monitor/' + id,
    method: 'delete'
  })
}

// 执行质量检查
export function runQualityCheck(id) {
  return request({
    url: '/datawarehouse/quality/check/' + id,
    method: 'post'
  })
}

// 获取质量统计信息
export function getQualityStats() {
  return request({
    url: '/datawarehouse/quality/stats',
    method: 'get'
  })
}

// 获取质量趋势数据
export function getQualityTrend(period) {
  return request({
    url: '/datawarehouse/quality/trend',
    method: 'get',
    params: { period }
  })
}

// 查询质量规则列表
export function listQualityRule(query) {
  return request({
    url: '/datawarehouse/quality/rule/list',
    method: 'get',
    params: query
  })
}

// 查询质量规则详细
export function getQualityRule(id) {
  return request({
    url: '/datawarehouse/quality/rule/' + id,
    method: 'get'
  })
}

// 新增质量规则
export function addQualityRule(data) {
  return request({
    url: '/datawarehouse/quality/rule',
    method: 'post',
    data: data
  })
}

// 修改质量规则
export function updateQualityRule(data) {
  return request({
    url: '/datawarehouse/quality/rule',
    method: 'put',
    data: data
  })
}

// 删除质量规则
export function deleteQualityRule(id) {
  return request({
    url: '/datawarehouse/quality/rule/' + id,
    method: 'delete'
  })
}

// 获取质量检查历史
export function getQualityCheckHistory(id, query) {
  return request({
    url: '/datawarehouse/quality/history/' + id,
    method: 'get',
    params: query
  })
}

// 获取质量报告
export function getQualityReport(id) {
  return request({
    url: '/datawarehouse/quality/report/' + id,
    method: 'get'
  })
}

// 导出质量报告
export function exportQualityReport(query) {
  return request({
    url: '/datawarehouse/quality/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取质量规则模板
export function getQualityRuleTemplates() {
  return request({
    url: '/datawarehouse/quality/rule/templates',
    method: 'get'
  })
}

// 验证质量规则
export function validateQualityRule(data) {
  return request({
    url: '/datawarehouse/quality/rule/validate',
    method: 'post',
    data: data
  })
}

// 获取表字段信息
export function getTableFields(tableName) {
  return request({
    url: '/datawarehouse/quality/table/fields',
    method: 'get',
    params: { tableName }
  })
}

// 获取质量异常详情
export function getQualityIssues(id, query) {
  return request({
    url: '/datawarehouse/quality/issues/' + id,
    method: 'get',
    params: query
  })
}

// 处理质量异常
export function handleQualityIssue(id, action, comment) {
  return request({
    url: '/datawarehouse/quality/issues/' + id + '/handle',
    method: 'post',
    data: { action, comment }
  })
}

// 获取质量评分详情
export function getQualityScoreDetail(id) {
  return request({
    url: '/datawarehouse/quality/score/' + id,
    method: 'get'
  })
}

// 设置质量阈值
export function setQualityThreshold(data) {
  return request({
    url: '/datawarehouse/quality/threshold',
    method: 'post',
    data: data
  })
}

// 获取质量阈值配置
export function getQualityThreshold(tableId) {
  return request({
    url: '/datawarehouse/quality/threshold/' + tableId,
    method: 'get'
  })
}
