import Layout from '@/layout'

const datawarehouseRouter = {
  path: '/datawarehouse',
  component: Layout,
  redirect: '/datawarehouse/etl',
  name: 'Datawarehouse',
  meta: {
    title: '数据仓库',
    icon: 'database'
  },
  children: [
    {
      path: 'etl',
      component: () => import('@/views/datawarehouse/etl/index'),
      name: 'EtlTask',
      meta: { 
        title: 'ETL任务管理', 
        icon: 'operation',
        keepAlive: true
      }
    },
    {
      path: 'etl/create',
      component: () => import('@/views/datawarehouse/etl/create'),
      name: 'EtlTaskCreate',
      meta: { 
        title: '新增ETL任务', 
        activeMenu: '/datawarehouse/etl'
      },
      hidden: true
    },
    {
      path: 'etl/edit/:id(\\d+)',
      component: () => import('@/views/datawarehouse/etl/create'),
      name: 'EtlTaskEdit',
      meta: { 
        title: '编辑ETL任务', 
        activeMenu: '/datawarehouse/etl'
      },
      hidden: true
    },
    {
      path: 'etl/detail/:id(\\d+)',
      component: () => import('@/views/datawarehouse/etl/detail'),
      name: 'EtlTaskDetail',
      meta: { 
        title: 'ETL任务详情', 
        activeMenu: '/datawarehouse/etl'
      },
      hidden: true
    },
    {
      path: 'lineage',
      component: () => import('@/views/datawarehouse/lineage/index'),
      name: 'DataLineage',
      meta: { 
        title: '数据血缘', 
        icon: 'tree',
        keepAlive: true
      }
    },
    {
      path: 'quality',
      component: () => import('@/views/datawarehouse/quality/index'),
      name: 'DataQuality',
      meta: { 
        title: '数据质量', 
        icon: 'monitor',
        keepAlive: true
      }
    },
    {
      path: 'quality/detail/:id(\\d+)',
      component: () => import('@/views/datawarehouse/quality/detail'),
      name: 'DataQualityDetail',
      meta: { 
        title: '质量详情', 
        activeMenu: '/datawarehouse/quality'
      },
      hidden: true
    },
    {
      path: 'quality/rule/create',
      component: () => import('@/views/datawarehouse/quality/rule'),
      name: 'QualityRuleCreate',
      meta: { 
        title: '新增质量规则', 
        activeMenu: '/datawarehouse/quality'
      },
      hidden: true
    },
    {
      path: 'quality/rule/:id(\\d+)',
      component: () => import('@/views/datawarehouse/quality/rule'),
      name: 'QualityRuleEdit',
      meta: { 
        title: '编辑质量规则', 
        activeMenu: '/datawarehouse/quality'
      },
      hidden: true
    },
    {
      path: 'quality/report/:id(\\d+)',
      component: () => import('@/views/datawarehouse/quality/report'),
      name: 'QualityReport',
      meta: { 
        title: '质量报告', 
        activeMenu: '/datawarehouse/quality'
      },
      hidden: true
    },
    {
      path: 'schedule',
      component: () => import('@/views/datawarehouse/schedule/index'),
      name: 'EtlSchedule',
      meta: { 
        title: '调度管理', 
        icon: 'time',
        keepAlive: true
      }
    },
    {
      path: 'schedule/create',
      component: () => import('@/views/datawarehouse/schedule/create'),
      name: 'ScheduleCreate',
      meta: { 
        title: '新增调度', 
        activeMenu: '/datawarehouse/schedule'
      },
      hidden: true
    },
    {
      path: 'schedule/edit/:id(\\d+)',
      component: () => import('@/views/datawarehouse/schedule/create'),
      name: 'ScheduleEdit',
      meta: { 
        title: '编辑调度', 
        activeMenu: '/datawarehouse/schedule'
      },
      hidden: true
    },
    {
      path: 'schedule/detail/:id(\\d+)',
      component: () => import('@/views/datawarehouse/schedule/detail'),
      name: 'ScheduleDetail',
      meta: { 
        title: '调度详情', 
        activeMenu: '/datawarehouse/schedule'
      },
      hidden: true
    },
    {
      path: 'monitor',
      component: () => import('@/views/datawarehouse/monitor/index'),
      name: 'DatawarehouseMonitor',
      meta: { 
        title: '运行监控', 
        icon: 'monitor',
        keepAlive: true
      }
    },
    {
      path: 'config',
      component: () => import('@/views/datawarehouse/config/index'),
      name: 'DatawarehouseConfig',
      meta: { 
        title: '配置管理', 
        icon: 'setting',
        keepAlive: true
      }
    }
  ]
}

export default datawarehouseRouter
