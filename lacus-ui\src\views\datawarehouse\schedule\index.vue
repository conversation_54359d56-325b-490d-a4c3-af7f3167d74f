<template>
  <div class="app-container">
    <!-- 调度概览 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card class="schedule-card">
          <div class="schedule-item">
            <div class="schedule-value">{{ scheduleStats.totalJobs }}</div>
            <div class="schedule-label">总任务数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="schedule-card">
          <div class="schedule-item">
            <div class="schedule-value running">{{ scheduleStats.runningJobs }}</div>
            <div class="schedule-label">运行中</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="schedule-card">
          <div class="schedule-item">
            <div class="schedule-value success">{{ scheduleStats.successJobs }}</div>
            <div class="schedule-label">今日成功</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="schedule-card">
          <div class="schedule-item">
            <div class="schedule-value danger">{{ scheduleStats.failedJobs }}</div>
            <div class="schedule-label">今日失败</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 调度日历 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>调度日历</span>
          <div>
            <el-button type="primary" @click="handleTodaySchedule">今日调度</el-button>
            <el-button type="success" @click="handleRefreshCalendar">刷新</el-button>
          </div>
        </div>
      </template>
      <el-calendar v-model="calendarDate" @panel-change="handlePanelChange">
        <template #date-cell="{ data }">
          <div class="calendar-cell">
            <div class="date-number">{{ data.day.split('-').slice(-1)[0] }}</div>
            <div class="schedule-indicators" v-if="getDateSchedules(data.day).length > 0">
              <el-badge
                v-for="schedule in getDateSchedules(data.day).slice(0, 3)"
                :key="schedule.id"
                :type="getScheduleType(schedule.status)"
                :value="schedule.taskName.substring(0, 2)"
                class="schedule-badge"
              />
              <span v-if="getDateSchedules(data.day).length > 3" class="more-indicator">
                +{{ getDateSchedules(data.day).length - 3 }}
              </span>
            </div>
          </div>
        </template>
      </el-calendar>
    </el-card>

    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="任务名称" prop="taskName">
        <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="调度状态" prop="scheduleStatus">
        <el-select v-model="queryParams.scheduleStatus" placeholder="请选择调度状态" clearable>
          <el-option label="启用" value="ENABLED"/>
          <el-option label="禁用" value="DISABLED"/>
          <el-option label="暂停" value="PAUSED"/>
        </el-select>
      </el-form-item>
      <el-form-item label="执行状态" prop="executionStatus">
        <el-select v-model="queryParams.executionStatus" placeholder="请选择执行状态" clearable>
          <el-option label="等待中" value="WAITING"/>
          <el-option label="运行中" value="RUNNING"/>
          <el-option label="成功" value="SUCCESS"/>
          <el-option label="失败" value="FAILED"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增调度</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="VideoPlay" @click="handleBatchStart" :disabled="multiple">批量启动</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="VideoPause" @click="handleBatchPause" :disabled="multiple">批量暂停</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" @click="handleBatchDelete" :disabled="multiple">批量删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="scheduleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true"/>
      <el-table-column label="Cron表达式" align="center" prop="cronExpression" width="150"/>
      <el-table-column label="下次执行时间" align="center" prop="nextFireTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.nextFireTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="调度状态" align="center" prop="scheduleStatus" width="100">
        <template #default="scope">
          <el-tag :type="getScheduleStatusType(scope.row.scheduleStatus)">
            {{ getScheduleStatusText(scope.row.scheduleStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="执行状态" align="center" prop="executionStatus" width="100">
        <template #default="scope">
          <el-tag :type="getExecutionStatusType(scope.row.executionStatus)">
            {{ getExecutionStatusText(scope.row.executionStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最后执行时间" align="center" prop="lastFireTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastFireTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执行次数" align="center" prop="fireCount" width="100"/>
      <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button-group>
            <el-tooltip content="查看详情" placement="top">
              <el-button type="primary" icon="View" @click="handleDetail(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="编辑" placement="top">
              <el-button type="warning" icon="Edit" @click="handleUpdate(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="立即执行" placement="top">
              <el-button type="success" icon="VideoPlay" @click="handleTrigger(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip :content="scope.row.scheduleStatus === 'ENABLED' ? '暂停' : '启动'" placement="top">
              <el-button
                :type="scope.row.scheduleStatus === 'ENABLED' ? 'warning' : 'success'"
                :icon="scope.row.scheduleStatus === 'ENABLED' ? 'VideoPause' : 'VideoPlay'"
                @click="handleToggleStatus(scope.row)"
                size="small"
              />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="danger" icon="Delete" @click="handleDelete(scope.row)" size="small"/>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 调度任务创建/编辑弹出层 -->
    <ScheduleCreate
      v-model:visible="createDialogVisible"
      :schedule-id="currentScheduleId"
      @success="handleCreateSuccess"
    />

    <!-- 今日调度对话框 -->
    <el-dialog title="今日调度任务" v-model="todayScheduleVisible" width="80%">
      <el-table :data="todaySchedules" style="width: 100%">
        <el-table-column label="任务名称" prop="taskName"/>
        <el-table-column label="计划执行时间" prop="scheduledTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.scheduledTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际执行时间" prop="actualTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="执行状态" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getExecutionStatusType(scope.row.status)">
              {{ getExecutionStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="执行时长" prop="duration" width="100">
          <template #default="scope">
            <span>{{ formatDuration(scope.row.duration) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleViewLog(scope.row)">查看日志</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="EtlSchedule">
import { listScheduleJob, getScheduleStats, getTodaySchedules, getCalendarSchedules,
         triggerScheduleJob, pauseScheduleJob, resumeScheduleJob, deleteScheduleJob } from '@/api/datawarehouse/schedule';
import ScheduleCreate from './create.vue';

const { proxy } = getCurrentInstance();
const router = useRouter();

const scheduleList = ref([]);
const scheduleStats = ref({
  totalJobs: 0,
  runningJobs: 0,
  successJobs: 0,
  failedJobs: 0
});
const calendarSchedules = ref([]);
const todaySchedules = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const calendarDate = ref(new Date());
const todayScheduleVisible = ref(false);
const createDialogVisible = ref(false);
const currentScheduleId = ref(null);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    scheduleStatus: undefined,
    executionStatus: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询调度任务列表 */
function getList() {
  loading.value = true;
  listScheduleJob(queryParams.value).then(response => {
    scheduleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取调度统计 */
function getStats() {
  getScheduleStats().then(response => {
    scheduleStats.value = response.data;
  });
}

/** 获取日历调度数据 */
function getCalendarData(year, month) {
  getCalendarSchedules(year, month).then(response => {
    calendarSchedules.value = response.data;
  });
}

/** 获取指定日期的调度任务 */
function getDateSchedules(date) {
  return calendarSchedules.value.filter(schedule =>
    schedule.scheduleDate === date
  );
}

/** 日历面板变化 */
function handlePanelChange(date) {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  getCalendarData(year, month);
}

/** 今日调度 */
function handleTodaySchedule() {
  getTodaySchedules().then(response => {
    todaySchedules.value = response.data;
    todayScheduleVisible.value = true;
  });
}

/** 刷新日历 */
function handleRefreshCalendar() {
  const year = calendarDate.value.getFullYear();
  const month = calendarDate.value.getMonth() + 1;
  getCalendarData(year, month);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增调度 */
function handleAdd() {
  currentScheduleId.value = null;
  createDialogVisible.value = true;
}

/** 查看详情 */
function handleDetail(row) {
  router.push(`/datawarehouse/schedule/detail/${row.id}`);
}

/** 编辑 */
function handleUpdate(row) {
  currentScheduleId.value = row.id || ids.value;
  createDialogVisible.value = true;
}

/** 创建/编辑成功回调 */
function handleCreateSuccess() {
  createDialogVisible.value = false;
  getList();
}

/** 立即执行 */
function handleTrigger(row) {
  proxy.$modal.confirm(`确认要立即执行"${row.taskName}"调度任务吗？`).then(() => {
    return triggerScheduleJob(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("任务已触发执行");
    getList();
  });
}

/** 切换状态 */
function handleToggleStatus(row) {
  const action = row.scheduleStatus === 'ENABLED' ? '暂停' : '启动';
  proxy.$modal.confirm(`确认要${action}"${row.taskName}"调度任务吗？`).then(() => {
    const api = row.scheduleStatus === 'ENABLED' ? pauseScheduleJob : resumeScheduleJob;
    return api(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess(`${action}成功`);
    getList();
  });
}

/** 删除 */
function handleDelete(row) {
  const ids = row.id || ids.value;
  proxy.$modal.confirm(`是否确认删除调度任务编号为"${ids}"的数据项？`).then(() => {
    return deleteScheduleJob(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  });
}

/** 批量启动 */
function handleBatchStart() {
  proxy.$modal.confirm(`确认要启动选中的${ids.value.length}个调度任务吗？`).then(() => {
    return resumeScheduleJob(ids.value.join(','));
  }).then(() => {
    proxy.$modal.msgSuccess("批量启动成功");
    getList();
  });
}

/** 批量暂停 */
function handleBatchPause() {
  proxy.$modal.confirm(`确认要暂停选中的${ids.value.length}个调度任务吗？`).then(() => {
    return pauseScheduleJob(ids.value.join(','));
  }).then(() => {
    proxy.$modal.msgSuccess("批量暂停成功");
    getList();
  });
}

/** 批量删除 */
function handleBatchDelete() {
  handleDelete();
}

/** 查看日志 */
function handleViewLog(row) {
  router.push(`/datawarehouse/etl/log/${row.executionId}`);
}

/** 获取调度状态类型 */
function getScheduleStatusType(status) {
  const typeMap = {
    'ENABLED': 'success',
    'DISABLED': 'info',
    'PAUSED': 'warning'
  };
  return typeMap[status] || 'info';
}

/** 获取调度状态文本 */
function getScheduleStatusText(status) {
  const textMap = {
    'ENABLED': '启用',
    'DISABLED': '禁用',
    'PAUSED': '暂停'
  };
  return textMap[status] || status;
}

/** 获取执行状态类型 */
function getExecutionStatusType(status) {
  const typeMap = {
    'WAITING': 'info',
    'RUNNING': 'warning',
    'SUCCESS': 'success',
    'FAILED': 'danger'
  };
  return typeMap[status] || 'info';
}

/** 获取执行状态文本 */
function getExecutionStatusText(status) {
  const textMap = {
    'WAITING': '等待中',
    'RUNNING': '运行中',
    'SUCCESS': '成功',
    'FAILED': '失败'
  };
  return textMap[status] || status;
}

/** 获取调度类型 */
function getScheduleType(status) {
  const typeMap = {
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'RUNNING': 'warning'
  };
  return typeMap[status] || 'info';
}

/** 格式化执行时长 */
function formatDuration(duration) {
  if (!duration) return '-';
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

onMounted(() => {
  getList();
  getStats();
  handleRefreshCalendar();
});
</script>

<style scoped>
.schedule-card {
  text-align: center;
}

.schedule-item {
  padding: 20px 0;
}

.schedule-value {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
}

.schedule-value.running {
  color: #E6A23C;
}

.schedule-value.success {
  color: #67C23A;
}

.schedule-value.danger {
  color: #F56C6C;
}

.schedule-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.calendar-cell {
  height: 80px;
  padding: 4px;
}

.date-number {
  font-weight: bold;
  margin-bottom: 4px;
}

.schedule-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.schedule-badge {
  font-size: 10px;
}

.more-indicator {
  font-size: 10px;
  color: #999;
}
</style>
