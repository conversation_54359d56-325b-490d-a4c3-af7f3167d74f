package com.lacus.common.core.domain.model;

import com.lacus.common.enums.ExecutionStatus;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * ETL执行结果模型
 * 
 * <AUTHOR>
 */
@Data
public class EtlExecutionResult {
    
    /**
     * 执行状态
     */
    private ExecutionStatus status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 处理行数
     */
    private Long processedRows;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTimeMs;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 执行详情
     */
    private String executionDetails;
    
    /**
     * 数据质量检查结果
     */
    private QualityCheckResult qualityResult;
    
    /**
     * 获取执行时长（秒）
     */
    public Long getExecutionTimeSeconds() {
        if (executionTimeMs != null) {
            return executionTimeMs / 1000;
        }
        return null;
    }
    
    /**
     * 判断是否执行成功
     */
    public boolean isSuccess() {
        return ExecutionStatus.SUCCESS.equals(status);
    }
    
    /**
     * 判断是否执行失败
     */
    public boolean isFailed() {
        return ExecutionStatus.FAILED.equals(status);
    }
}

/**
 * 数据质量检查结果
 */
@Data
class QualityCheckResult {
    
    /**
     * 检查状态
     */
    private String status;
    
    /**
     * 通过的规则数量
     */
    private Integer passedRules;
    
    /**
     * 失败的规则数量
     */
    private Integer failedRules;
    
    /**
     * 警告的规则数量
     */
    private Integer warningRules;
    
    /**
     * 质量分数
     */
    private Double qualityScore;
    
    /**
     * 检查详情
     */
    private String details;
}
