import axios from 'axios';
import {ElMessage} from 'element-plus';
import {saveAs} from 'file-saver';
import {getToken} from '@/utils/token';
import errorCode from '@/utils/errorCode';
import {isBlobData} from '@/utils/common';

const baseURL = import.meta.env.VITE_APP_BASE_API;

export default {
    name(name, isDelete = true) {
        const url = `${baseURL}/file/download?fileName=${encodeURI(name)}&delete=${isDelete}`;
        axios({
            method: 'get',
            url,
            responseType: 'blob',
            headers: {Authorization: `Bearer ${getToken()}`},
        }).then(async (res) => {
            const isLogin = await isBlobData(res.data);
            if (isLogin) {
                const blob = new Blob([res.data]);
                this.saveAs(blob, decodeURI(res.headers['download-filename']));
            } else {
                this.printErrMsg(res.data);
            }
        });
    },
    zip(url, name) {
        const fullURL = baseURL + url;
        axios({
            method: 'get',
            fullURL,
            responseType: 'blob',
            headers: {Authorization: `Bearer ${getToken()}`},
        }).then(async (res) => {
            const isLogin = await isBlobData(res.data);
            if (isLogin) {
                const blob = new Blob([res.data], {type: 'application/zip'});
                this.saveAs(blob, name);
            } else {
                this.printErrMsg(res.data);
            }
        });
    },
    saveAs(text, name, opts) {
        saveAs(text, name, opts);
    },
    async printErrMsg(data) {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode.default;
        ElMessage.error(errMsg);
    },
};
