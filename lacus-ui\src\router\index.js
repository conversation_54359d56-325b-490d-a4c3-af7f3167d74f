import { createWebHistory, createRouter } from 'vue-router';
import Layout from '@/layout';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true,
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' },
      },
    ],
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' },
      },
    ],
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' },
      },
    ],
  },
  {
    path: '/metadata/table-manager',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:tableId(\\d+)',
        component: () => import('@/views/metadata/table/detail'),
        name: 'tableDetail',
        meta: { title: '表详情', activeMenu: '/metadata/table' },
      },
    ],
  },
  {
    path: '/datasync/job-manager',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'addJob',
        component: () => import('@/views/datasync/job/job'),
        name: 'addJob',
        meta: { title: '新建任务', activeMenu: '/datasync/job' },
      },
    ],
  },
  {
    path: '/datasync/job-manager',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'editJob/:jobId(.+)',
        component: () => import('@/views/datasync/job/job'),
        name: 'editJob',
        meta: { title: '编辑任务', activeMenu: '/datasync/job' },
      },
    ],
  },
  {
    path: '/datasync/job-manager',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:jobId(\\d+)',
        component: () => import('@/views/datasync/job/detail'),
        name: 'jobDetail',
        meta: { title: '任务详情', activeMenu: '/datasync/job' },
      },
    ],
  },
  {
    path: '/flink',
    component: Layout,
    hidden: true,
    meta: { title: 'Flink开发', icon: 'monitor' },
    children: [
      {
        path: 'job',
        component: () => import('@/views/flink/job/index'),
        name: 'FlinkJob',
        meta: { title: '任务定义' },
      },
      {
        path: 'job/add/:type',
        component: () => import('@/views/flink/job/sql'),
        name: 'AddFlinkJob',
        meta: { title: '新增Flink任务', activeMenu: '/flink/job' },
        hidden: true,
      },
      {
        path: 'job/add/jar', // 单独配置jar路由
        component: () => import('@/views/flink/job/jar'),
        name: 'AddFlinkJarJob',
        meta: { title: '新增Jar任务', activeMenu: '/flink/job' },
        hidden: true,
      },
      {
        path: 'job/edit/:jobId',
        component: () => import('@/views/flink/job/edit'),
        name: 'EditFlinkJob',
        meta: { title: '编辑Flink任务', activeMenu: '/flink/job' },
        hidden: true,
      },
      {
        path: 'job/detail/:jobId',
        component: () => import('@/views/flink/job/detail'),
        name: 'FlinkJobDetail',
        meta: { title: 'Flink任务详情', activeMenu: '/flink/job' },
        hidden: true,
      },
      {
        path: 'instance',
        component: () => import('@/views/flink/instance/index'),
        name: 'FlinkJobInstance',
        meta: { title: '任务实例', activeMenu: '/flink/instance' },
      },
      {
        path: 'instance/detail/:instanceId',
        component: () => import('@/views/flink/instance/detail'),
        name: 'FlinkJobInstanceDetail',
        meta: { title: '实例详情', activeMenu: '/flink/instance' },
        hidden: true,
      },
    ],
  },
  {
    path: '/spark',
    component: Layout,
    hidden: true,
    meta: { title: 'Spark开发', icon: 'monitor' },
    children: [
      {
        path: 'job',
        component: () => import('@/views/spark/job/index'),
        name: 'SparkJob',
        meta: { title: '任务定义' },
      },
      {
        path: 'job/add/sql',
        component: () => import('@/views/spark/job/sql'),
        name: 'AddSparkSqlJob',
        meta: { title: '新增SQL任务', activeMenu: '/spark/job' },
        hidden: true,
      },
      {
        path: 'job/add/jar',
        component: () => import('@/views/spark/job/jar'),
        name: 'AddSparkJarJob',
        meta: { title: '新增Jar任务', activeMenu: '/spark/job' },
        hidden: true,
      },
      {
        path: 'job/detail/:jobId',
        component: () => import('@/views/spark/job/detail'),
        name: 'SparkJobDetail',
        meta: { title: 'Spark任务详情', activeMenu: '/spark/job' },
        hidden: true,
      },
      {
        path: 'instance',
        component: () => import('@/views/spark/instance/index'),
        name: 'SparkJobInstance',
        meta: { title: '任务实例' },
      },
      {
        path: 'instance/detail/:instanceId',
        component: () => import('@/views/spark/instance/detail'),
        name: 'SparkJobInstanceDetail',
        meta: { title: '实例详情', activeMenu: '/spark/instance' },
        hidden: true,
      },
      {
        path: 'job',
        name: 'SparkJob',
        component: () => import('@/views/spark/job/index'),
        meta: { title: 'Spark任务管理' },
      },
      {
        path: 'job/sql',
        name: 'SparkSqlJob',
        component: () => import('@/views/spark/job/sql'),
        meta: { title: '新建SQL任务', activeMenu: '/spark/job' },
        hidden: true,
      },
      {
        path: 'job/jar',
        name: 'SparkJarJob',
        component: () => import('@/views/spark/job/jar'),
        meta: { title: '新建JAR任务', activeMenu: '/spark/job' },
        hidden: true,
      },
      {
        path: 'job/edit/:jobId',
        name: 'SparkJobEdit',
        component: () => import('@/views/spark/job/edit'),
        meta: { title: '编辑任务', activeMenu: '/spark/job' },
        hidden: true,
      },
    ],
  },
  {
    path: '/oneapi',
    component: Layout,
    hidden: true,
    meta: { title: '统一API', icon: 'monitor' },
    children: [
      {
        path: 'oneapi',
        component: () => import('@/views/oneapi/index'),
        name: 'oneApi',
        meta: { title: 'API定义' },
      },
      {
        path: 'add/',
        component: () => import('@/views/oneapi/create'),
        name: 'AddApi',
        meta: { title: '新增Api', activeMenu: '/oneapi' },
        hidden: true,
      },
      {
        path: 'edit/:apiId',
        component: () => import('@/views/oneapi/edit'),
        name: 'EditApi',
        meta: { title: '编辑Api', activeMenu: '/oneapi' },
        hidden: true,
      },
      {
        path: 'detail/:apiId',
        component: () => import('@/views/oneapi/detail'),
        name: 'ApiDetail',
        meta: { title: 'Api详情', activeMenu: '/oneapi' },
        hidden: true,
      }
    ],
  },
  // // 数据仓库ETL管理
  // {
  //   path: '/datawarehouse',
  //   component: Layout,
  //   redirect: '/datawarehouse/etl',
  //   name: 'Datawarehouse',
  //   meta: {
  //     title: '数据仓库',
  //     icon: 'database'
  //   },
  //   children: [
  //     {
  //       path: 'etl',
  //       component: () => import('@/views/datawarehouse/etl/index'),
  //       name: 'EtlTask',
  //       meta: {
  //         title: 'ETL任务管理',
  //         icon: 'operation',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'lineage',
  //       component: () => import('@/views/datawarehouse/lineage/index'),
  //       name: 'DataLineage',
  //       meta: {
  //         title: '数据血缘',
  //         icon: 'tree',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'quality',
  //       component: () => import('@/views/datawarehouse/quality/index'),
  //       name: 'DataQuality',
  //       meta: {
  //         title: '数据质量',
  //         icon: 'monitor',
  //         keepAlive: true
  //       }
  //     },
  //     {
  //       path: 'schedule',
  //       component: () => import('@/views/datawarehouse/schedule/index'),
  //       name: 'EtlSchedule',
  //       meta: {
  //         title: '调度管理',
  //         icon: 'time',
  //         keepAlive: true
  //       }
  //     }
  //   ]
  // },
  // // 数据仓库ETL相关的隐藏路由
  // {
  //   path: '/datawarehouse/etl-manager',
  //   component: Layout,
  //   hidden: true,
  //   children: [
  //     {
  //       path: 'create',
  //       component: () => import('@/views/datawarehouse/etl/create'),
  //       name: 'EtlTaskCreate',
  //       meta: {
  //         title: '新增ETL任务',
  //         activeMenu: '/datawarehouse/etl'
  //       }
  //     },
  //     {
  //       path: 'edit/:id(\\d+)',
  //       component: () => import('@/views/datawarehouse/etl/create'),
  //       name: 'EtlTaskEdit',
  //       meta: {
  //         title: '编辑ETL任务',
  //         activeMenu: '/datawarehouse/etl'
  //       }
  //     },
  //     {
  //       path: 'detail/:id(\\d+)',
  //       component: () => import('@/views/datawarehouse/etl/detail'),
  //       name: 'EtlTaskDetail',
  //       meta: {
  //         title: 'ETL任务详情',
  //         activeMenu: '/datawarehouse/etl'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/datawarehouse/quality-manager',
  //   component: Layout,
  //   hidden: true,
  //   children: [
  //     {
  //       path: 'detail/:id(\\d+)',
  //       component: () => import('@/views/datawarehouse/quality/detail'),
  //       name: 'DataQualityDetail',
  //       meta: {
  //         title: '质量详情',
  //         activeMenu: '/datawarehouse/quality'
  //       }
  //     },
  //     {
  //       path: 'rule/create',
  //       component: () => import('@/views/datawarehouse/quality/rule'),
  //       name: 'QualityRuleCreate',
  //       meta: {
  //         title: '新增质量规则',
  //         activeMenu: '/datawarehouse/quality'
  //       }
  //     },
  //     {
  //       path: 'rule/:id(\\d+)',
  //       component: () => import('@/views/datawarehouse/quality/rule'),
  //       name: 'QualityRuleEdit',
  //       meta: {
  //         title: '编辑质量规则',
  //         activeMenu: '/datawarehouse/quality'
  //       }
  //     },
  //     {
  //       path: 'report/:id(\\d+)',
  //       component: () => import('@/views/datawarehouse/quality/report'),
  //       name: 'QualityReport',
  //       meta: {
  //         title: '质量报告',
  //         activeMenu: '/datawarehouse/quality'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/datawarehouse/schedule-manager',
  //   component: Layout,
  //   hidden: true,
  //   children: [
  //     {
  //       path: 'create',
  //       component: () => import('@/views/datawarehouse/schedule/create'),
  //       name: 'ScheduleCreate',
  //       meta: {
  //         title: '新增调度',
  //         activeMenu: '/datawarehouse/schedule'
  //       }
  //     },
  //     {
  //       path: 'edit/:id(\\d+)',
  //       component: () => import('@/views/datawarehouse/schedule/create'),
  //       name: 'ScheduleEdit',
  //       meta: {
  //         title: '编辑调度',
  //         activeMenu: '/datawarehouse/schedule'
  //       }
  //     },
  //     {
  //       path: 'detail/:id(\\d+)',
  //       component: () => import('@/views/datawarehouse/schedule/detail'),
  //       name: 'ScheduleDetail',
  //       meta: {
  //         title: '调度详情',
  //         activeMenu: '/datawarehouse/schedule'
  //       }
  //     }
  //   ]
  // },
];

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    }
    return { top: 0 };
  },
});

export default router;
