<template>
    <div class="top-right-btn">
        <el-row>
            <el-tooltip class="item" effect="dark" :content="showSearch ? '隐藏搜索' : '显示搜索'" placement="top">
                <el-button circle icon="Search" @click="toggleSearch()"/>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="刷新" placement="top">
                <el-button circle icon="Refresh" @click="refresh()"/>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="显隐列" placement="top" v-if="columns">
                <el-button circle icon="Menu" @click="showColumn()"/>
            </el-tooltip>
        </el-row>
        <el-dialog :title="title" v-model="open" append-to-body>
            <el-transfer
                    :titles="['显示', '隐藏']"
                    v-model="value"
                    :data="columns"
                    @change="dataChange"
            ></el-transfer>
        </el-dialog>
    </div>
</template>

<script setup>
const props = defineProps({
    showSearch: {
        type: Boolean,
        default: true,
    },
    columns: {
        type: Array,
    },
});

const emits = defineEmits(['update:showSearch', 'queryTable']);

// 显隐数据
const value = ref([]);
// 弹出层标题
const title = ref('显示/隐藏');
// 是否显示弹出层
const open = ref(false);

// 搜索
function toggleSearch() {
    emits('update:showSearch', !props.showSearch);
}

// 刷新
function refresh() {
    emits('queryTable');
}

// 右侧列表元素变化
function dataChange(data) {
    for (const item in props.columns) {
        const {key} = props.columns[item];
        props.columns[item].visible = !data.includes(key);
    }
}

// 打开显隐列dialog
function showColumn() {
    open.value = true;
}

// 显隐列初始默认隐藏列
for (const item in props.columns) {
    if (props.columns[item].visible === false) {
        value.value.push(parseInt(item));
    }
}
</script>

<style lang='scss' scoped>
:deep(.el-transfer__button) {
  border-radius: 50%;
  display: block;
  margin-left: 0px;
}

:deep(.el-transfer__button:first-child) {
  margin-bottom: 10px;
}

.my-el-transfer {
  text-align: center;
}
</style>
