<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>ETL任务详情</span>
          <div>
            <el-button @click="handleBack">返回</el-button>
            <el-button type="warning" @click="handleEdit">编辑</el-button>
            <el-button type="success" @click="handleRun" :loading="running">执行任务</el-button>
          </div>
        </div>
      </template>

      <!-- 基本信息 -->
      <el-card class="mb-4">
        <template #header>基本信息</template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务名称">{{ taskDetail.taskName }}</el-descriptions-item>
          <el-descriptions-item label="任务描述">{{ taskDetail.description }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="taskDetail.status === 1 ? 'success' : 'danger'">
              {{ taskDetail.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据流向">
            <el-tag type="info" size="small">{{ taskDetail.sourceLayer }}</el-tag>
            <el-icon style="margin: 0 5px;"><Right/></el-icon>
            <el-tag type="success" size="small">{{ taskDetail.targetLayer }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="调度方式">
            <dict-tag :options="schedule_type" :value="taskDetail.scheduleType"/>
          </el-descriptions-item>
          <el-descriptions-item label="Cron表达式" v-if="taskDetail.scheduleType === 'CRON'">
            {{ taskDetail.cronExpression }}
          </el-descriptions-item>
          <el-descriptions-item label="目标表">{{ taskDetail.targetTable }}</el-descriptions-item>
          <el-descriptions-item label="写入模式">
            <el-tag>{{ getWriteModeText(taskDetail.writeMode) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(taskDetail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ parseTime(taskDetail.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="最后执行时间">{{ parseTime(taskDetail.lastRunTime) }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 数据源配置 -->
      <el-card class="mb-4">
        <template #header>数据源配置</template>
        <el-table :data="taskDetail.sourceTables" style="width: 100%">
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="源表名" prop="tableName"/>
          <el-table-column label="表别名" prop="alias"/>
          <el-table-column label="过滤条件" prop="whereCondition" :show-overflow-tooltip="true"/>
        </el-table>
      </el-card>

      <!-- 字段映射配置 -->
      <el-card class="mb-4">
        <template #header>字段映射配置</template>
        <el-table :data="taskDetail.fieldMappings" style="width: 100%">
          <el-table-column label="序号" type="index" width="60"/>
          <el-table-column label="目标字段" prop="targetField"/>
          <el-table-column label="字段类型" prop="fieldType" width="120">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.fieldType }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="转换表达式" prop="expression" :show-overflow-tooltip="true"/>
          <el-table-column label="默认值" prop="defaultValue"/>
        </el-table>
      </el-card>

      <!-- 执行历史 -->
      <el-card class="mb-4">
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>执行历史</span>
            <el-button type="primary" size="small" @click="refreshExecutionHistory">刷新</el-button>
          </div>
        </template>
        <el-table :data="executionHistory" style="width: 100%" v-loading="historyLoading">
          <el-table-column label="执行ID" prop="executionId" width="120"/>
          <el-table-column label="执行状态" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" prop="startTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.startTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束时间" prop="endTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.endTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="执行时长" prop="duration" width="100">
            <template #default="scope">
              <span>{{ formatDuration(scope.row.duration) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="处理记录数" prop="processedRecords" width="120"/>
          <el-table-column label="错误信息" prop="errorMessage" :show-overflow-tooltip="true"/>
          <el-table-column label="操作" width="150">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleViewLog(scope.row)">查看日志</el-button>
              <el-button type="info" size="small" @click="handleViewMetrics(scope.row)">查看指标</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>

    <!-- 执行日志对话框 -->
    <el-dialog title="执行日志" v-model="logDialogVisible" width="80%" :before-close="handleCloseLogDialog">
      <div class="log-container">
        <pre>{{ executionLog }}</pre>
      </div>
    </el-dialog>

    <!-- 执行指标对话框 -->
    <el-dialog title="执行指标" v-model="metricsDialogVisible" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="输入记录数">{{ currentMetrics.inputRecords }}</el-descriptions-item>
        <el-descriptions-item label="输出记录数">{{ currentMetrics.outputRecords }}</el-descriptions-item>
        <el-descriptions-item label="错误记录数">{{ currentMetrics.errorRecords }}</el-descriptions-item>
        <el-descriptions-item label="跳过记录数">{{ currentMetrics.skippedRecords }}</el-descriptions-item>
        <el-descriptions-item label="平均处理速度">{{ currentMetrics.avgProcessingRate }} 条/秒</el-descriptions-item>
        <el-descriptions-item label="内存使用">{{ currentMetrics.memoryUsage }} MB</el-descriptions-item>
        <el-descriptions-item label="CPU使用率">{{ currentMetrics.cpuUsage }}%</el-descriptions-item>
        <el-descriptions-item label="网络IO">{{ currentMetrics.networkIO }} MB</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="EtlTaskDetail">
import { getEtlTask, runEtlTask, getExecutionHistory, getExecutionLog, getExecutionMetrics } from '@/api/datawarehouse/etl';

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const { schedule_type } = proxy.useDict('schedule_type');

const taskDetail = ref({});
const executionHistory = ref([]);
const historyLoading = ref(false);
const running = ref(false);
const logDialogVisible = ref(false);
const metricsDialogVisible = ref(false);
const executionLog = ref('');
const currentMetrics = ref({});

/** 获取任务详情 */
function getTaskDetail() {
  getEtlTask(route.params.id).then(response => {
    taskDetail.value = response.data;
  });
}

/** 获取执行历史 */
function getExecutionHistoryData() {
  historyLoading.value = true;
  getExecutionHistory(route.params.id).then(response => {
    executionHistory.value = response.data;
  }).finally(() => {
    historyLoading.value = false;
  });
}

/** 刷新执行历史 */
function refreshExecutionHistory() {
  getExecutionHistoryData();
}

/** 执行任务 */
function handleRun() {
  proxy.$modal.confirm(`确认要执行"${taskDetail.value.taskName}"任务吗？`).then(() => {
    running.value = true;
    return runEtlTask(route.params.id);
  }).then(() => {
    proxy.$modal.msgSuccess("任务执行成功");
    refreshExecutionHistory();
  }).finally(() => {
    running.value = false;
  });
}

/** 编辑任务 */
function handleEdit() {
  router.push(`/datawarehouse/etl/edit/${route.params.id}`);
}

/** 返回列表 */
function handleBack() {
  router.push('/datawarehouse/etl');
}

/** 查看执行日志 */
function handleViewLog(row) {
  getExecutionLog(row.executionId).then(response => {
    executionLog.value = response.data;
    logDialogVisible.value = true;
  });
}

/** 查看执行指标 */
function handleViewMetrics(row) {
  getExecutionMetrics(row.executionId).then(response => {
    currentMetrics.value = response.data;
    metricsDialogVisible.value = true;
  });
}

/** 关闭日志对话框 */
function handleCloseLogDialog() {
  logDialogVisible.value = false;
  executionLog.value = '';
}

/** 获取写入模式文本 */
function getWriteModeText(mode) {
  const modeMap = {
    'OVERWRITE': '覆盖写入',
    'APPEND': '追加写入',
    'UPSERT': '更新插入'
  };
  return modeMap[mode] || mode;
}

/** 获取状态类型 */
function getStatusType(status) {
  const typeMap = {
    'RUNNING': 'warning',
    'SUCCESS': 'success',
    'FAILED': 'danger',
    'CANCELLED': 'info'
  };
  return typeMap[status] || 'info';
}

/** 获取状态文本 */
function getStatusText(status) {
  const textMap = {
    'RUNNING': '运行中',
    'SUCCESS': '成功',
    'FAILED': '失败',
    'CANCELLED': '已取消'
  };
  return textMap[status] || status;
}

/** 格式化执行时长 */
function formatDuration(duration) {
  if (!duration) return '-';
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

onMounted(() => {
  getTaskDetail();
  getExecutionHistoryData();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 16px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-container pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
