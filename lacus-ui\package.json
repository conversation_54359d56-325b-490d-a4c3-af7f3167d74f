{"name": "lacus", "version": "1.0.0", "description": "lacus", "author": "lacus", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "fix": "eslint src/**/*.* --fix"}, "repository": {"type": "git", "url": "https://github.com/eyesmoons/lacus-ui"}, "dependencies": {"@element-plus/icons-vue": "1.1.4", "@vueuse/core": "^9.3.0", "aos": "^2.3.4", "axios": "^1.6.1", "echarts": "5.3.2", "element-plus": "2.2.20", "file-saver": "2.0.5", "fuse.js": "6.5.3", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "nprogress": "0.2.0", "qiankun": "^2.10.15", "three": "^0.171.0", "vue": "3.2.31", "vue-cropper": "1.0.3", "vue-router": "4.0.14", "vuex": "4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "2.3.1", "@vue/compiler-sfc": "3.2.31", "eslint": "^8.21.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-config-standard": "^17.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.2.4", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-vue": "^9.7.0", "monaco-editor": "^0.34.0", "prettier": "2.7.1", "sass": "1.50.0", "unplugin-auto-import": "^0.16.7", "vite": "^2.9.16", "vite-plugin-compression": "0.5.1", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-svg-icons": "^0.7.0", "vite-plugin-vue-setup-extend": "0.4.0"}}