import request from '@/utils/request'

// 查询表列表
export function listTable(query) {
  return request({
    url: '/metadata/table/list',
    method: 'get',
    params: query
  })
}

// 查询表详细信息
export function getTable(tableId) {
  return request({
    url: '/metadata/table/' + tableId,
    method: 'get'
  })
}

// 根据数据层级获取表列表
export function getTablesByLayer(layer) {
  return request({
    url: '/metadata/table/by-layer',
    method: 'get',
    params: { layer }
  })
}

// 获取表字段信息
export function getTableFields(tableName) {
  return request({
    url: '/metadata/table/fields/' + tableName,
    method: 'get'
  })
}

// 获取表统计信息
export function getTableStats(tableName) {
  return request({
    url: '/metadata/table/stats/' + tableName,
    method: 'get'
  })
}

// 获取表血缘关系
export function getTableLineage(tableName) {
  return request({
    url: '/metadata/table/lineage/' + tableName,
    method: 'get'
  })
}

// 获取表的数据预览
export function getTablePreview(tableName, limit = 100) {
  return request({
    url: '/metadata/table/preview/' + tableName,
    method: 'get',
    params: { limit }
  })
}

// 获取表的DDL语句
export function getTableDDL(tableName) {
  return request({
    url: '/metadata/table/ddl/' + tableName,
    method: 'get'
  })
}

// 同步表元数据
export function syncTableMetadata(tableName) {
  return request({
    url: '/metadata/table/sync/' + tableName,
    method: 'post'
  })
}

// 获取数据库列表
export function getDatabaseList(datasourceId) {
  return request({
    url: '/metadata/database/list',
    method: 'get',
    params: { datasourceId }
  })
}

// 获取数据源列表
export function getDatasourceList() {
  return request({
    url: '/metadata/datasource/list',
    method: 'get'
  })
}
