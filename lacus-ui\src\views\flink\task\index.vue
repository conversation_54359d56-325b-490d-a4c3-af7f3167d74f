<template>
  <div class="pipeline-config">
    <!-- 层级选择 -->
    <el-select v-model="currentLayer" @change="resetForm">
      <el-option label="ODS→DWD" value="dwd"/>
      <el-option label="DWD→DWS" value="dws"/>
      <el-option label="DWS→ADS" value="ads"/>
    </el-select>

    <!-- 字段映射配置 -->
    <div v-for="(field,index) in fieldMappings" :key="index">
      <el-input v-model="field.source" placeholder="源字段"/>
      <el-select v-model="field.transformType">
        <el-option label="直接映射" value="direct"/>
        <el-option label="性别转换" value="gender"/>
        <el-option label="手机号加密" value="encrypt"/>
      </el-select>
      <el-input v-model="field.target" placeholder="目标字段"/>
    </div>

    <!-- 过滤条件 -->
    <el-input 
      v-model="filterCondition"
      placeholder="过滤条件表达式 例如: age > 18"
    />

    <el-button @click="generateCode">生成代码模板</el-button>
  </div>
</template>
<script setup type="ts">
import { onMounted } from 'vue';

onMounted(() => {
  console.log('Index page mounted');
});
</script>